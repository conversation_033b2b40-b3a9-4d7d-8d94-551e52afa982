const express = require('express');
const bodyParser = require('body-parser');
const cors = require('cors');
const { generatePDFWithRetry, generateJPEGWithRetry } = require('./new_html_to_pdf');

// 加载配置
const config = require('./pdf_service_config');

// 创建Express应用
const app = express();

// 配置中间件
app.use(cors(config.CORS)); // 使用配置的CORS设置
app.use(bodyParser.json({ limit: config.PERFORMANCE.requestBodySizeLimit }));
app.use(bodyParser.urlencoded({ extended: true, limit: config.PERFORMANCE.requestBodySizeLimit }));

// TODO 配置 url
// 健康检查接口
app.get('/health', (req, res) => {
  console.log('收到健康检查请求');
  res.json({ status: 'ok', timestamp: new Date().toISOString() });
});

// TODO 配置 url
// HTML到PDF转换接口
app.post('/convertpdf', async (req, res) => {
  try {
    console.log('收到PDF转换请求');
    const { html, options = {} } = req.body;
    
    if (!html) {
      return res.status(400).json({ error: '缺少HTML内容' });
    }
    
    console.log('HTML长度:', html.length);
    console.log('转换选项:', options);
    
    // 设置基础URL用于解析相对路径
    // const baseUrl = options.baseUrl || `http://localhost:${PDF_SERVICE_PORT}`;
    
    // 转换HTML为PDF
    console.log('开始转换HTML到PDF...');
    const pdfBuffer = await generatePDFWithRetry(html, {
      ...options,
    //   baseUrl
    });
    
    // 设置响应头并发送PDF数据
    res.setHeader('Content-Type', 'application/pdf');
    res.setHeader('Content-Disposition', `attachment; filename="${options.filename || 'resume.pdf'}"`);
    res.setHeader('Content-Length', pdfBuffer.length);
    
    res.send(pdfBuffer);
    console.log('PDF转换完成并已发送');
  } catch (error) {
    console.error('PDF转换失败:', error);
    res.status(500).json({ 
      error: '转换PDF时出错', 
      message: error.message,
      stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
    });
  }
});

// HTML到JPEG转换接口
app.post('/convertjpeg', async (req, res) => {
  try {
    console.log('收到JPEG转换请求');
    const { html, options = {} } = req.body;
    
    if (!html) {
      return res.status(400).json({ error: '缺少HTML内容' });
    }
    
    console.log('HTML长度:', html.length);
    console.log('转换选项:', options);
    
    // 转换HTML为JPEG
    console.log('开始转换HTML到JPEG...');
    const jpegBuffer = await generateJPEGWithRetry(html, {
      ...options,
    });
    
    // 设置响应头并发送JPEG数据
    res.setHeader('Content-Type', 'image/jpeg');
    res.setHeader('Content-Disposition', `attachment; filename="${options.filename || 'resume.jpeg'}"`);
    res.setHeader('Content-Length', jpegBuffer.length);
    
    res.send(jpegBuffer);
    console.log('JPEG转换完成并已发送');
  } catch (error) {
    console.error('JPEG转换失败:', error);
    res.status(500).json({ 
      error: '转换JPEG时出错', 
      message: error.message,
      stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
    });
  }
});

// 启动服务器
app.listen(config.PORT, config.HOST, () => {
  config.printConfig();
  console.log(`健康检查: ${config.getServerUrl()}/health`);
  console.log(`PDF转换接口: ${config.getServerUrl()}/convertpdf (POST)`);
  console.log(`JPEG转换接口: ${config.getServerUrl()}/convertjpeg (POST)`);
});