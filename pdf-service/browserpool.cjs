const puppeteer = require('puppeteer-core');
const { v4: uuidv4 } = require('uuid');
const { Mutex } = require('async-mutex');
const fs = require('fs');

// 获取Chrome可执行文件路径
// const config = require('./pdf_service_config');
// const chromePath = '/home/<USER>/.cache/puppeteer/chrome-headless-shell/linux-136.0.7103.92/chrome-headless-shell-linux64/chrome-headless-shell';

/**
 * 浏览器实例类
 */
class Browser {
  constructor(id, browserOptions) {
    this.id = id;
    this.browser = null;
    this.pages = new Map(); // [pageId] => { page, createdAt }
    this.usingPages = 0;    // 当前正在使用的页面数
    this.totalPages = 0;    // 总创建页面数
    this.usageCount = 0;    // 使用次数
    this.status = 'initializing'; // initializing | ready | restarting | error | closed
    this.browserOptions = browserOptions;
    this.creationTime = Date.now();
    this.lastUsedTime = Date.now();
    this.mutex = new Mutex(); // 添加互斥锁，确保并发操作安全
    this.restartTimeoutId = null;
    this.timeToRestartBrowser = 5000; // 重启等待时间，单位毫秒
    this.timeoutDefaultNavigation = 30000;
    this.timeoutDefault = 30000;
    this.errorCount = 0; // 错误计数
  }

  /**
   * 初始化浏览器实例
   */
  async initialize() {
    try {
      const startTime = Date.now();
      console.log(`Browser ${this.id}: Launching puppeteer with options:`, JSON.stringify(this.browserOptions));

      try {
        this.browser = await puppeteer.launch(this.browserOptions);
        console.log(`Browser ${this.id}: Puppeteer launched successfully`);
      } catch (launchError) {
        console.error(`Browser ${this.id}: Failed to launch puppeteer:`, launchError);
        throw launchError;
      }

      // 监听浏览器断开连接事件
      this.browser.on('disconnected', () => {
        console.log(`Browser ${this.id} disconnected`);
        this.status = 'error';
      });

      // 重置所有统计数据 - 这是修复重启循环的关键
      this.status = 'ready';
      this.creationTime = Date.now();
      this.lastUsedTime = Date.now();
      this.errorCount = 0;
      this.usageCount = 0;        // 重置使用次数
      this.totalPages = 0;        // 重置总页面数
      this.usingPages = 0;        // 重置当前使用页面数
      this.pages.clear();         // 清空页面集合

      console.log(`Browser ${this.id} initialize time: ${Date.now() - startTime}ms`);
      return this;
    } catch (error) {
      this.status = 'error';
      this.errorCount++;
      console.error(`Browser ${this.id} initialization error:`, error);
      throw error;
    }
  }

  /**
   * 获取新页面
   */
  async getPage() {
    const release = await this.mutex.acquire();
    try {
      if (this.status !== 'ready') {
        throw new Error(`Browser ${this.id} is not ready, current status: ${this.status}`);
      }

      const pageId = uuidv4();
      const page = await this.browser.newPage();

      // 设置页面超时
      page.setDefaultNavigationTimeout(this.timeoutDefaultNavigation);
      page.setDefaultTimeout(this.timeoutDefault);

      // 监听页面错误
      page.on('error', (error) => {
        console.error(`Page ${pageId} error:`, error);
        this.errorCount++;
      });

      // 存储页面信息
      this.pages.set(pageId, {
        page,
        createdAt: Date.now(),
        errors: 0
      });

      this.usingPages++;
      this.totalPages++;
      this.usageCount++;
      this.lastUsedTime = Date.now();

      return { page, pageId, browserId: this.id };
    } catch (error) {
      console.error(`Error getting page from browser ${this.id}:`, error);
      this.errorCount++;
      throw error;
    } finally {
      release();
    }
  }

  /**
   * 释放页面
   */
  async releasePage(pageId) {
    const release = await this.mutex.acquire();
    try {
      const pageInfo = this.pages.get(pageId);
      if (!pageInfo) return;

      try {
        await pageInfo.page.close();
      } catch (e) {
        console.error(`Error closing page ${pageId}:`, e);
        this.errorCount++;
      }

      this.pages.delete(pageId);
      this.usingPages--;
      this.lastUsedTime = Date.now();
    } catch (error) {
      console.error(`Error releasing page ${pageId}:`, error);
      this.errorCount++;
    } finally {
      release();
    }
  }

  /**
   * 关闭浏览器
   */
  async close() {
    const release = await this.mutex.acquire();
    try {
      // 设置状态为关闭中，防止其他操作
      this.status = 'closing';

      // 关闭所有页面，使用Promise.all和超时机制
      const pageClosePromises = [];
      for (const [pageId, pageInfo] of this.pages) {
        // 为每个页面关闭操作添加超时
        const pageClosePromise = Promise.race([
          pageInfo.page.close().catch(e => {
            console.error(`Error closing page ${pageId} during browser close:`, e);
          }),
          new Promise(resolve => setTimeout(() => {
            console.warn(`Timeout closing page ${pageId}, continuing...`);
            resolve();
          }, 5000)) // 5秒超时
        ]);
        pageClosePromises.push(pageClosePromise);
      }

      // 等待所有页面关闭，但设置总超时
      await Promise.race([
        Promise.all(pageClosePromises),
        new Promise(resolve => setTimeout(() => {
          console.warn(`Timeout waiting for all pages to close in browser ${this.id}, continuing...`);
          resolve();
        }, 10000)) // 10秒总超时
      ]);

      // 清空页面集合
      this.pages.clear();
      this.usingPages = 0;

      // 关闭浏览器，添加超时
      if (this.browser) {
        await Promise.race([
          this.browser.close(),
          new Promise(resolve => setTimeout(() => {
            console.warn(`Timeout closing browser ${this.id}, forcing cleanup...`);
            resolve();
          }, 10000)) // 10秒超时
        ]);
      }

      this.status = 'closed';
      console.log(`Browser ${this.id} closed successfully`);
    } catch (error) {
      console.error(`Error closing browser ${this.id}:`, error);
      this.status = 'error';
      this.errorCount++;

      // 即使出错，也确保清理资源
      this.pages.clear();
      this.usingPages = 0;
      this.browser = null;
    } finally {
      release();
    }
  }

  /**
   * 检查浏览器健康状态
   */
  async checkHealth() {
    try {
      if (!this.browser || this.status === 'closed' || this.status === 'error') {
        return {
          isAlive: false,
          creationTime: this.creationTime,
          lastUsedTime: this.lastUsedTime,
          totalPages: this.totalPages,
          usingPages: this.usingPages,
          usageCount: this.usageCount,
          errorCount: this.errorCount,
          status: this.status
        };
      }

      // 检查浏览器是否仍然连接
      // isConnected()方法已弃用，但我们仍然使用它进行健康检查
      // 如果将来完全移除，可以使用其他方法如尝试获取版本信息来检查连接状态
      let isConnected = false;
      try {
        isConnected = typeof this.browser.isConnected === 'function' ? this.browser.isConnected() : true;
      } catch (e) {
        console.error(`Error checking browser connection for ${this.id}:`, e);
        isConnected = false;
      }

      // 尝试获取版本信息作为健康检查
      let version;
      try {
        version = await this.browser.version();
      } catch (e) {
        console.error(`Error getting browser version for ${this.id}:`, e);
        this.errorCount++;
        return {
          isAlive: false,
          creationTime: this.creationTime,
          lastUsedTime: this.lastUsedTime,
          totalPages: this.totalPages,
          usingPages: this.usingPages,
          usageCount: this.usageCount,
          errorCount: this.errorCount,
          status: this.status
        };
      }

      return {
        isAlive: isConnected && version !== undefined,
        creationTime: this.creationTime,
        lastUsedTime: this.lastUsedTime,
        totalPages: this.totalPages,
        usingPages: this.usingPages,
        usageCount: this.usageCount,
        errorCount: this.errorCount,
        status: this.status
      };
    } catch (error) {
      console.error(`Error checking health for browser ${this.id}:`, error);
      this.errorCount++;
      return {
        isAlive: false,
        creationTime: this.creationTime,
        lastUsedTime: this.lastUsedTime,
        totalPages: this.totalPages,
        usingPages: this.usingPages,
        usageCount: this.usageCount,
        errorCount: this.errorCount,
        status: this.status
      };
    }
  }

  /**
   * 重启浏览器
   */
  async restart() {
    if (this.restartTimeoutId) clearTimeout(this.restartTimeoutId);
    this.status = 'restarting';

    console.log(`Browser ${this.id} scheduled for restart in ${this.timeToRestartBrowser}ms`);

    this.restartTimeoutId = setTimeout(async () => {
      const release = await this.mutex.acquire();
      try {
        // 确保所有页面已关闭
        for (const [pageId, pageInfo] of this.pages) {
          try {
            await pageInfo.page.close();
          } catch (e) {
            console.error(`Error closing page ${pageId} during restart:`, e);
          }
        }

        this.pages.clear();
        this.usingPages = 0;

        // 关闭旧浏览器
        if (this.browser) {
          await this.browser.close();
        }

        // 初始化新浏览器
        await this.initialize();
        console.log(`Browser ${this.id} restarted successfully - usageCount: ${this.usageCount}, totalPages: ${this.totalPages}, status: ${this.status}`);
      } catch (error) {
        console.error(`Error restarting browser ${this.id}:`, error);
        this.status = 'error';
        this.errorCount++;
      } finally {
        release();
      }
    }, this.timeToRestartBrowser);
  }
}

/**
 * 浏览器池类
 */
class BrowserPool {
  constructor(options) {
    // 默认配置与用户配置合并
    this.config = {
      initialSize: 2,                        // 初始浏览器数量
      maxBrowsers: 4,                        // 最大浏览器实例数
      minBrowsers: 1,                        // 最小浏览器实例数
      maxPagesPerBrowser: 5,                 // 每个浏览器最多创建的页面数
      maxTotalUsage: 100,                    // 单个浏览器最大使用次数
      maxAvgUsage: 10,                       // 平均使用阈值，超过此值创建新实例
      minAvgUsage: 3,                        // 最小平均使用阈值，低于此值关闭多余实例
      maxErrorCount: 5,                      // 最大错误次数，超过此值重启浏览器
      maxIdleTime: 10 * 60 * 1000,            // 最大空闲时间，单位毫秒（5分钟）
      maxLifeTime: 60 * 60 * 1000,           // 最大生命周期，单位毫秒（1小时）
      intervalHealthCheck: 15000,            // 健康检查间隔，单位毫秒
      intervalScaleCheck: 30000,             // 扩缩容检查间隔，单位毫秒
      ...options
    };

    this.browsers = new Map();               // 存储所有浏览器实例
    this.mutex = new Mutex();                // 互斥锁，确保并发操作安全
    this.healthCheckIntervalId = null;       // 健康检查定时器ID
    this.scaleCheckIntervalId = null;        // 扩缩容检查定时器ID
    this.isShuttingDown = false;             // 是否正在关闭
  }

  /**
   * 初始化浏览器池
   */
  async initializePool() {
    // const release = await this.mutex.acquire();
    // try {
    // 创建初始浏览器实例
    await this.createBrowsers(this.config.initialSize);

    // 启动健康检查
    this.startHealthCheck();

    // 启动扩缩容检查
    this.startScaleCheck();

    // 保存到全局变量，便于进程退出时访问
    global.browserpool = this;

    console.log(`Browser pool initialized with ${this.browsers.size} browsers`);
    // } catch (error) {
    //   console.error('Error initializing browser pool:', error);
    // } finally {
    //   release();
    // }
  }

  /**
   * 创建多个浏览器实例
   */
  async createBrowsers(count) {
    console.log(`Creating ${count} browsers...`);
    for (let i = 0; i < count; i++) {
      console.log(`Creating browser ${i+1}/${count}...`);
      // 直接创建浏览器，不使用互斥锁
      const id = uuidv4();
      console.log(`Creating browser with ID: ${id}`);
      const browser = new Browser(id, this.config.browserOptions);

      try {
        console.log(`Initializing browser ${id}...`);
        await browser.initialize();
        console.log(`Browser ${id} initialized successfully`);
        this.browsers.set(id, browser);
        console.log(`Created new browser ${id}, total browsers: ${this.browsers.size}`);
      } catch (error) {
        console.error(`Failed to initialize browser ${id}:`, error);
      }
    }
    console.log(`Created ${this.browsers.size} browsers`);
  }

  /**
   * 创建单个浏览器实例
   */
  async createBrowser() {
    console.log(`Acquiring mutex for createBrowser...`);
    const release = await this.mutex.acquire();
    console.log(`Mutex acquired for createBrowser`);
    try {
      // 检查是否达到最大浏览器数量
      if (this.browsers.size >= this.config.maxBrowsers) {
        console.log(`Cannot create more browsers, reached max limit (${this.config.maxBrowsers})`);
        return null;
      }

      const id = uuidv4();
      console.log(`Creating browser with ID: ${id}`);
      const browser = new Browser(id, this.config.browserOptions);

      try {
        console.log(`Initializing browser ${id}...`);
        await browser.initialize();
        console.log(`Browser ${id} initialized successfully`);
        this.browsers.set(id, browser);
        console.log(`Created new browser ${id}, total browsers: ${this.browsers.size}`);
        return id;
      } catch (error) {
        console.error(`Failed to initialize browser ${id}:`, error);
        return null;
      }
    } finally {
      console.log(`Releasing mutex for createBrowser...`);
      release();
      console.log(`Mutex released for createBrowser`);
    }
  }

  /**
   * 重启浏览器
   */
  async restartBrowser(browserId) {
    // const release = await this.mutex.acquire();
    // try {
    const browser = this.browsers.get(browserId);
    if (!browser) return;

    console.log(`Restarting browser ${browserId}`);

    try {
      await browser.restart();
    } catch (error) {
      console.error(`Restart failed for browser ${browserId}:`, error);

      // 如果重启失败，删除此浏览器并创建新的
      this.browsers.delete(browserId);
      await this.createBrowser();
    }
    // } finally {
    //   release();
    // }
  }

  /**
   * 关闭浏览器
   */
  async closeBrowser(browserId) {
    const release = await this.mutex.acquire();
    try {
      const browser = this.browsers.get(browserId);
      if (!browser) {
        console.log(`Browser ${browserId} not found, skipping close`);
        return;
      }

      // 只关闭没有活跃页面的浏览器
      if (browser.usingPages > 0) {
        console.log(`Cannot close browser ${browserId}, it has ${browser.usingPages} active pages`);
        return;
      }

      console.log(`Closing browser ${browserId}`);

      try {
        // 添加超时机制，确保即使browser.close()卡住也能继续
        await Promise.race([
          browser.close(),
          new Promise(resolve => setTimeout(() => {
            console.warn(`Timeout closing browser ${browserId}, forcing cleanup...`);
            // 使用resolve而不是reject，避免进入catch块，而是直接继续执行
            this.browsers.delete(browserId);
            resolve();
          }, 15000)) // 15秒超时
        ]);

        // 如果browser.close()正常完成，确保从Map中删除
        this.browsers.delete(browserId);
        console.log(`Browser ${browserId} closed, total browsers: ${this.browsers.size}`);
      } catch (error) {
        console.error(`Failed to close browser ${browserId}:`, error);
        // 即使出错，也从Map中删除浏览器
        this.browsers.delete(browserId);
        console.log(`Removed browser ${browserId} from pool after error, total browsers: ${this.browsers.size}`);
      }
    } catch (error) {
      console.error(`Unexpected error in closeBrowser for ${browserId}:`, error);
      // 确保在任何情况下都从Map中删除浏览器
      this.browsers.delete(browserId);
    } finally {
      release();
    }
  }

  /**
   * 选择最适合的浏览器
   */
  selectBrowser(candidates) {
    // 按照使用页面数量、总使用次数和错误次数进行加权选择
    const weights = candidates.map(browser => {
      // 基础权重：计算剩余容量比例（0到1之间）
      // 确保即使usingPages > maxPagesPerBrowser也能得到合理的权重
      const remainingCapacityRatio = Math.max(0, 1 - (browser.usingPages / this.config.maxPagesPerBrowser));
      let weight = 5 * remainingCapacityRatio; // 基础权重范围：0-5

      // 使用次数权重：使用次数越少越好
      const usageRatio = browser.usageCount / this.config.maxTotalUsage;
      const usageWeight = 3 * (1 - Math.min(1, usageRatio)); // 使用次数权重范围：0-3

      // 错误次数权重：错误越少越好
      const errorRatio = browser.errorCount / this.config.maxErrorCount;
      const errorWeight = 2 * (1 - Math.min(1, errorRatio)); // 错误权重范围：0-2

      // 总权重 = 基础权重 + 使用次数权重 + 错误权重
      const totalWeight = weight + usageWeight + errorWeight;

      // 确保权重至少为0.1，避免完全排除某个浏览器
      return {
        browser,
        weight: Math.max(0.1, totalWeight),
        stats: {
          usingPages: browser.usingPages,
          totalPages: browser.totalPages,
          usageCount: browser.usageCount,
          errorCount: browser.errorCount,
          remainingCapacityRatio,
          usageRatio,
          errorRatio
        }
      };
    });

    // 记录权重信息，便于调试
    if (candidates.length > 1) {
      console.log('Browser selection weights:', weights.map(w => ({
        id: w.browser.id,
        weight: w.weight.toFixed(2),
        usingPages: w.stats.usingPages,
        usageCount: w.stats.usageCount,
        errorCount: w.stats.errorCount
      })));
    }

    // 计算总权重
    const totalWeight = weights.reduce((sum, item) => sum + item.weight, 0);

    // 随机选择
    let random = Math.random() * totalWeight;
    for (const item of weights) {
      random -= item.weight;
      if (random <= 0) {
        if (candidates.length > 1) {
          console.log(`Selected browser ${item.browser.id} with weight ${item.weight.toFixed(2)}`);
        }
        return item.browser;
      }
    }

    // 默认返回第一个
    console.log(`Falling back to first browser ${candidates[0].id}`);
    return candidates[0];
  }

  /**
   * 获取页面
   */
  async getPage() {
    // const release = await this.mutex.acquire();
    // try {
      if (this.isShuttingDown) {
        throw new Error('Browser pool is shutting down');
      }

      // 筛选可用的浏览器
      const candidates = Array.from(this.browsers.values())
        .filter(browser => {
          // 状态必须为ready
          if (browser.status !== 'ready') return false;

          // 页面数量不能超过上限
          // if (browser.usingPages >= this.config.maxPagesPerBrowser) return false;

          // 使用次数不能超过上限
          // if (browser.usageCount >= this.config.maxTotalUsage) return false;

          // 错误次数不能超过上限
          // if (browser.errorCount >= this.config.maxErrorCount) return false;

          return true;
        });

      if (candidates.length > 0) {
        // 选择最适合的浏览器
        const browser = this.selectBrowser(candidates);
        return await browser.getPage();
      } else {
        // 没有可用浏览器，等待0.5秒后重试
        console.log('Browser pool getPage: 没有状态为ready的浏览器，等待0.5秒后重试');

        // 等待0.5秒
        await new Promise(resolve => setTimeout(resolve, 500));

        // 重新筛选可用的浏览器
        const retryCandidate = Array.from(this.browsers.values())
          .filter(browser => browser.status === 'ready');

        if (retryCandidate.length > 0) {
          console.log('Browser pool getPage: 重试成功，找到可用浏览器');
          const browser = this.selectBrowser(retryCandidate);
          return await browser.getPage();
        } else {
          console.log('Browser pool getPage: 重试失败，仍然没有可用浏览器');
          // 没有可用浏览器，尝试创建新的
          console.log('Browser pool getPage: 没有可用浏览器，尝试创建新浏览器');
          const newBrowserId = await this.createBrowser();

          if (!newBrowserId) {
            throw new Error('Failed to create new browser');
          }

          const newBrowser = this.browsers.get(newBrowserId);
          if (newBrowser && newBrowser.status === 'ready') {
            return await newBrowser.getPage();
          } else {
            throw new Error('新创建的浏览器未就绪');
          }
        }
      }
    // } catch (error) {
    //   console.error('Browser pool getPage error:', error);
    //   throw error;
    // } finally {
    //   release();
    // }
  }

  /**
   * 释放页面
   */
  async releasePage({ pageId, browserId }) {
    try {
      const browser = this.browsers.get(browserId);
      if (!browser) {
        console.warn(`Browser ${browserId} not found when releasing page ${pageId}`);
        return;
      }

      await browser.releasePage(pageId);
    } catch (error) {
      console.error(`Error releasing page ${pageId} from browser ${browserId}:`, error);
    }
  }

  /**
   * 计算平均使用量
   */
  calculateAvgUsage() {
    if (this.browsers.size === 0) return 0;

    const total = Array.from(this.browsers.values())
      .reduce((sum, browser) => sum + browser.usingPages, 0);

    return total / this.browsers.size;
  }

  /**
   * 启动健康检查
   */
  startHealthCheck() {
    if (this.healthCheckIntervalId) {
      clearInterval(this.healthCheckIntervalId);
    }

    this.healthCheckIntervalId = setInterval(async () => {
      if (this.isShuttingDown) return;

      // const release = await this.mutex.acquire();
      // try {
        // console.log(`Running health check for ${this.browsers.size} browsers`);

        const now = Date.now();
        const browserEntries = Array.from(this.browsers.entries());

        for (const [id, browser] of browserEntries) {
          try {
            const health = await browser.checkHealth();

            // 检查是否需要重启浏览器
            const needsRestart = (
              // 浏览器不可用
              !health.isAlive ||
              // 使用次数超过上限
              browser.usageCount >= this.config.maxTotalUsage ||
              // 错误次数超过上限
              browser.errorCount >= this.config.maxErrorCount ||
              // 生命周期超过上限
              (now - browser.creationTime) > this.config.maxLifeTime
              // ||
              // // 页面数量超过上限
              // browser.totalPages >= this.config.maxPagesPerBrowser
            );

            if (needsRestart && browser.usingPages === 0 && browser.status !== 'restarting') {
              console.log(`Browser ${id} needs restart: isAlive=${health.isAlive}, usageCount=${browser.usageCount}, errorCount=${browser.errorCount}, age=${(now - browser.creationTime)/1000}s, totalPages=${browser.totalPages}`);
              await this.restartBrowser(id);
            }
          } catch (error) {
            console.error(`Error during health check for browser ${id}:`, error);
          }
        }
      // } catch (error) {
      //   console.error('Error during health check:', error);
      // } finally {
      //   release();
      // }
    }, this.config.intervalHealthCheck);
  }

  /**
   * 启动扩缩容检查
   */
  startScaleCheck() {
    if (this.scaleCheckIntervalId) {
      clearInterval(this.scaleCheckIntervalId);
    }

    this.scaleCheckIntervalId = setInterval(async () => {
      if (this.isShuttingDown) return;

      // const release = await this.mutex.acquire();
      // try {
        // 计算平均使用量
        const avgUsage = this.calculateAvgUsage();
        console.log(`目前浏览器 ${this.browsers.size} 个，平均使用 ${avgUsage.toFixed(2)} 个页面`);

        // 扩容：如果平均使用量超过阈值，创建新浏览器
        if (avgUsage > this.config.maxAvgUsage &&
            this.browsers.size < this.config.maxBrowsers) {
          console.log(`Average usage (${avgUsage.toFixed(2)}) exceeds threshold (${this.config.maxAvgUsage}), creating new browser`);
          await this.createBrowser();
        }

        // 缩容：如果平均使用量低于阈值，关闭多余浏览器
        if (avgUsage < this.config.minAvgUsage &&
            this.browsers.size > this.config.minBrowsers) {
          console.log(`Average usage (${avgUsage.toFixed(2)}) below threshold (${this.config.minAvgUsage}), considering closing browsers`);

          // 找出空闲时间最长的浏览器
          const idleBrowsers = Array.from(this.browsers.values())
            .filter(browser => browser.usingPages === 0 && browser.status === 'ready')
            .sort((a, b) => a.lastUsedTime - b.lastUsedTime);

          // 关闭最早空闲的浏览器，但保留最小数量
          if (idleBrowsers.length > 0 && this.browsers.size > this.config.minBrowsers) {
            const oldestBrowser = idleBrowsers[0];
            const idleTime = Date.now() - oldestBrowser.lastUsedTime;

            // 只关闭空闲时间超过阈值的浏览器
            if (idleTime > this.config.maxIdleTime) {
              console.log(`Closing idle browser ${oldestBrowser.id}, idle for ${idleTime/1000}s`);
              await this.closeBrowser(oldestBrowser.id);
            }
          }
        }
      // } catch (error) {
      //   console.error('Error during scale check:', error);
      // } finally {
      //   release();
      // }
    }, this.config.intervalScaleCheck);
  }

  /**
   * 关闭所有浏览器（异步方法）
   */
  async closeAllBrowsers() {
    const release = await this.mutex.acquire();
    try {
      this.isShuttingDown = true;

      // 停止所有定时器
      if (this.healthCheckIntervalId) {
        clearInterval(this.healthCheckIntervalId);
        this.healthCheckIntervalId = null;
      }

      if (this.scaleCheckIntervalId) {
        clearInterval(this.scaleCheckIntervalId);
        this.scaleCheckIntervalId = null;
      }

      console.log(`Closing all browsers (${this.browsers.size})`);

      // 关闭所有浏览器，为每个浏览器添加超时
      const promises = [];
      for (const [id, browser] of this.browsers) {
        promises.push(
          Promise.race([
            browser.close()
              .then(() => console.log(`Browser ${id} closed successfully`))
              .catch(error => console.error(`Error closing browser ${id}:`, error)),
            new Promise(resolve => setTimeout(() => {
              console.warn(`Timeout closing browser ${id}, forcing cleanup...`);
              resolve();
            }, 15000)) // 15秒超时
          ])
        );
      }

      // 设置总体超时，确保即使有浏览器卡住也能继续
      await Promise.race([
        Promise.allSettled(promises),
        new Promise(resolve => setTimeout(() => {
          console.warn('Timeout waiting for all browsers to close, forcing cleanup...');
          resolve();
        }, 30000)) // 30秒总超时
      ]);

      // 无论如何都清空浏览器集合
      this.browsers.clear();

      console.log('All browsers closed');
    } catch (error) {
      console.error('Error closing all browsers:', error);
      // 确保在出错时也清空浏览器集合
      this.browsers.clear();
    } finally {
      release();
    }
  }

  /**
   * 同步关闭所有浏览器（用于进程退出时）
   */
  closeAllBrowsersSync() {
    console.log(`Closing all browsers synchronously (${this.browsers.size})`);
    this.isShuttingDown = true;

    // 停止所有定时器
    if (this.healthCheckIntervalId) {
      clearInterval(this.healthCheckIntervalId);
      this.healthCheckIntervalId = null;
    }

    if (this.scaleCheckIntervalId) {
      clearInterval(this.scaleCheckIntervalId);
      this.scaleCheckIntervalId = null;
    }

    // 同步关闭所有浏览器，但设置超时保护
    const startTime = Date.now();
    const maxTime = 5000; // 最多等待5秒

    // 同步关闭所有浏览器
    for (const [id, browser] of this.browsers) {
      try {
        // 检查是否已经超时
        if (Date.now() - startTime > maxTime) {
          console.warn('Timeout reached during synchronous browser closing, skipping remaining browsers');
          break;
        }

        if (browser.browser && typeof browser.browser.isConnected === 'function' && browser.browser.isConnected()) {
          try {
            browser.browser.close(); // 同步调用，不使用await
            console.log(`Browser ${id} closed synchronously`);
          } catch (closeError) {
            console.error(`Error during synchronous close of browser ${id}:`, closeError);
          }
        }
      } catch (error) {
        console.error(`Error checking browser ${id} status synchronously:`, error);
      }
    }

    this.browsers.clear();
    console.log('All browsers closed synchronously');
  }
}

// 在进程退出前执行同步清理
process.on('exit', (code) => {
  console.log(`Process is about to exit with code: ${code}`);
  if (global.browserpool) {
    try {
      global.browserpool.closeAllBrowsersSync();
    } catch (error) {
      console.error('Error during synchronous browser cleanup:', error);
    }
  }
});

// 信号处理
['SIGINT', 'SIGTERM', 'SIGHUP', 'SIGUSR2'].forEach(signal => {
  process.on(signal, () => {
    console.log(`\nReceived ${signal} signal`);

    // 尝试优雅关闭
    if (global.browserpool) {
      global.browserpool.closeAllBrowsers()
        .then(() => {
          console.log('Browser pool closed successfully');
          process.exit(0);
        })
        .catch(error => {
          console.error('Failed to close browser pool:', error);
          process.exit(1);
        });

      // 设置超时，确保即使异步关闭失败也能退出
      setTimeout(() => {
        console.log('Shutdown timeout reached, forcing exit');
        process.exit(1);
      }, 5000);
    } else {
      process.exit(0);
    }
  });
});

// 导出模块
module.exports = {
  Browser,
  BrowserPool,
  // chromePath
};