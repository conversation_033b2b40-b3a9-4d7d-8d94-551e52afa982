# ===========================================
# 微信小程序简历服务配置文件
# ===========================================

# ===========================================
# 环境配置
# ===========================================
NODE_ENV=development
DEBUG=false

# ===========================================
# FastAPI服务配置
# ===========================================
# 应用基本配置
APP_NAME=Resume Service API
VERSION=1.0.0

# 服务器配置
FASTAPI_HOST=0.0.0.0
FASTAPI_PORT=18080

# JWT认证配置
SECRET_KEY=your-secret-key-change-in-production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# ===========================================
# 数据库配置
# ===========================================
DATABASE_URL=mysql+pymysql://resume_user:Resume123!@localhost/resume_service

# ===========================================
# 微信小程序配置
# ===========================================
WECHAT_APP_ID=your_wechat_app_id
WECHAT_APP_SECRET=your_wechat_app_secret
WECHAT_API_URL=https://api.weixin.qq.com/sns/jscode2session

# ===========================================
# PDF服务配置
# ===========================================
PDF_SERVICE_HOST=localhost
PDF_SERVICE_PORT=3001
PDF_SERVICE_URL=http://localhost:3001

# PDF生成配置
PDF_BROWSER_POOL_INITIAL_SIZE=2
PDF_BROWSER_POOL_MAX_BROWSERS=4
PDF_BROWSER_POOL_MAX_PAGES_PER_BROWSER=5
PDF_BROWSER_POOL_MAX_TOTAL_USAGE=100
PDF_BROWSER_POOL_MAX_AVG_USAGE=10
PDF_BROWSER_POOL_HEALTH_CHECK_INTERVAL=15000

# ===========================================
# 证件照服务配置
# ===========================================
IDPHOTO_SERVICE_URL=http://127.0.0.1:8080

# ===========================================
# 文件存储配置
# ===========================================
STATIC_FILES_DIR=static
TEMP_FILES_DIR=temp
TEMP_FILES_EXPIRE_HOURS=24

# ===========================================
# 日志配置
# ===========================================
LOG_LEVEL=INFO
LOG_FILE=logs/app.log

# ===========================================
# 安全配置
# ===========================================
CORS_ORIGINS=*
CORS_CREDENTIALS=true
CORS_METHODS=*
CORS_HEADERS=*

# ===========================================
# 性能配置
# ===========================================
REQUEST_BODY_SIZE_LIMIT=50mb
CLEANUP_INTERVAL_HOURS=1
