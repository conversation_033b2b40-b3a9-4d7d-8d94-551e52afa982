"""
共享配置工具
提供配置文件管理和验证的通用功能
"""
import os
import sys
from pathlib import Path
from typing import Dict, Any, Optional
from dotenv import load_dotenv

class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_dir: Optional[str] = None):
        """
        初始化配置管理器
        
        Args:
            config_dir: 配置文件目录，默认为当前文件所在目录
        """
        if config_dir is None:
            config_dir = os.path.dirname(__file__)
        
        self.config_dir = Path(config_dir)
        self.env_file = self.config_dir / '.env'
        self.env_example_file = self.config_dir / '.env.example'
        
        # 加载环境变量
        self.load_env()
    
    def load_env(self):
        """加载环境变量文件"""
        if self.env_file.exists():
            load_dotenv(self.env_file)
            print(f"✓ 已加载环境变量文件: {self.env_file}")
        elif self.env_example_file.exists():
            print(f"⚠️  未找到.env文件，请复制{self.env_example_file}为.env并配置")
        else:
            print("⚠️  未找到环境变量配置文件")
    
    def get_env(self, key: str, default: Any = None, required: bool = False) -> Any:
        """
        获取环境变量
        
        Args:
            key: 环境变量名
            default: 默认值
            required: 是否必需
            
        Returns:
            环境变量值
            
        Raises:
            ValueError: 当required=True且环境变量不存在时
        """
        value = os.getenv(key, default)
        
        if required and value is None:
            raise ValueError(f"必需的环境变量 {key} 未设置")
        
        return value
    
    def get_bool_env(self, key: str, default: bool = False) -> bool:
        """获取布尔类型环境变量"""
        value = os.getenv(key, str(default)).lower()
        return value in ('true', '1', 'yes', 'on')
    
    def get_int_env(self, key: str, default: int = 0) -> int:
        """获取整数类型环境变量"""
        try:
            return int(os.getenv(key, str(default)))
        except ValueError:
            print(f"⚠️  环境变量 {key} 不是有效的整数，使用默认值 {default}")
            return default
    
    def get_list_env(self, key: str, default: list = None, separator: str = ',') -> list:
        """获取列表类型环境变量"""
        if default is None:
            default = []
        
        value = os.getenv(key)
        if not value:
            return default
        
        return [item.strip() for item in value.split(separator) if item.strip()]
    
    def validate_required_vars(self, required_vars: Dict[str, str]) -> bool:
        """
        验证必需的环境变量
        
        Args:
            required_vars: 必需变量字典 {变量名: 描述}
            
        Returns:
            是否所有必需变量都已设置
        """
        missing_vars = []
        
        for var_name, description in required_vars.items():
            if not os.getenv(var_name):
                missing_vars.append(f"{var_name} ({description})")
        
        if missing_vars:
            print("❌ 以下必需的环境变量未设置:")
            for var in missing_vars:
                print(f"   - {var}")
            return False
        
        return True
    
    def create_env_from_example(self):
        """从示例文件创建.env文件"""
        if self.env_file.exists():
            print(f"⚠️  .env文件已存在: {self.env_file}")
            return False
        
        if not self.env_example_file.exists():
            print(f"❌ 示例文件不存在: {self.env_example_file}")
            return False
        
        try:
            import shutil
            shutil.copy2(self.env_example_file, self.env_file)
            print(f"✓ 已创建.env文件: {self.env_file}")
            print("请编辑.env文件并设置正确的配置值")
            return True
        except Exception as e:
            print(f"❌ 创建.env文件失败: {e}")
            return False
    
    def print_config_summary(self, config_dict: Dict[str, Any]):
        """打印配置摘要"""
        print("===========================================")
        print("配置摘要")
        print("===========================================")
        
        for section, values in config_dict.items():
            print(f"\n[{section}]")
            if isinstance(values, dict):
                for key, value in values.items():
                    # 隐藏敏感信息
                    if any(sensitive in key.lower() for sensitive in ['secret', 'key', 'password', 'token']):
                        display_value = "***" if value else "未设置"
                    else:
                        display_value = value
                    print(f"  {key}: {display_value}")
            else:
                print(f"  {values}")
        
        print("===========================================")

# 创建全局配置管理器实例
config_manager = ConfigManager()

def get_service_urls() -> Dict[str, str]:
    """获取所有服务的URL配置"""
    return {
        'fastapi': f"http://{config_manager.get_env('FASTAPI_HOST', '0.0.0.0')}:{config_manager.get_int_env('FASTAPI_PORT', 18080)}",
        'pdf_service': config_manager.get_env('PDF_SERVICE_URL', 'http://localhost:3001'),
        'idphoto_service': config_manager.get_env('IDPHOTO_SERVICE_URL', 'http://127.0.0.1:8080')
    }

def validate_all_services() -> bool:
    """验证所有服务的配置"""
    required_vars = {
        'FASTAPI_PORT': 'FastAPI服务端口',
        'PDF_SERVICE_PORT': 'PDF服务端口',
        'DATABASE_URL': '数据库连接字符串'
    }
    
    return config_manager.validate_required_vars(required_vars)

if __name__ == "__main__":
    # 命令行工具
    if len(sys.argv) > 1:
        command = sys.argv[1]
        
        if command == "create-env":
            config_manager.create_env_from_example()
        elif command == "validate":
            if validate_all_services():
                print("✓ 所有配置验证通过")
            else:
                print("❌ 配置验证失败")
                sys.exit(1)
        elif command == "urls":
            urls = get_service_urls()
            print("服务URL配置:")
            for service, url in urls.items():
                print(f"  {service}: {url}")
        else:
            print("可用命令: create-env, validate, urls")
    else:
        print("配置管理工具")
        print("使用方法: python shared_config.py <command>")
        print("可用命令:")
        print("  create-env  - 从示例文件创建.env文件")
        print("  validate    - 验证配置")
        print("  urls        - 显示服务URL配置")
