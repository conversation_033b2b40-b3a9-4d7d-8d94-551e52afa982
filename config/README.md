# 配置文件重构指南

## 概述

本项目的配置文件已经重构，统一管理FastAPI服务和PDF服务的配置。新的配置结构更加清晰、易于维护。

## 新的配置文件结构

```
config/
├── .env                    # 环境变量文件（主配置）
├── .env.example           # 环境变量模板
├── fastapi_config.py      # FastAPI服务配置
├── pdf_service_config.js  # PDF服务配置
├── shared_config.py       # 共享配置工具
└── README.md              # 本文档
```

## 迁移步骤

### 1. 创建环境变量文件

```bash
# 复制示例文件
cp config/.env.example config/.env

# 编辑配置文件
nano config/.env
```

### 2. 配置FastAPI服务

FastAPI服务现在使用 `config/fastapi_config.py`：

```python
from config.fastapi_config import settings

# 使用配置
app = FastAPI(title=settings.APP_NAME)
```

### 3. 配置PDF服务

PDF服务现在使用 `config/pdf_service_config.js`：

```javascript
const config = require('../config/pdf_service_config');

// 使用配置
app.listen(config.PORT, config.HOST, () => {
    config.printConfig();
});
```

## 配置项说明

### FastAPI服务配置

- **应用基本配置**: APP_NAME, VERSION, DEBUG
- **服务器配置**: FASTAPI_HOST, FASTAPI_PORT
- **数据库配置**: DATABASE_URL
- **JWT配置**: SECRET_KEY, ALGORITHM, ACCESS_TOKEN_EXPIRE_MINUTES
- **微信小程序配置**: WECHAT_APP_ID, WECHAT_APP_SECRET
- **外部服务配置**: PDF_SERVICE_URL, IDPHOTO_SERVICE_URL

### PDF服务配置

- **服务器配置**: PDF_SERVICE_HOST, PDF_SERVICE_PORT
- **浏览器池配置**: PDF_BROWSER_POOL_*
- **性能配置**: REQUEST_BODY_SIZE_LIMIT
- **文件配置**: TEMP_FILES_DIR, TEMP_FILES_EXPIRE_HOURS

## 向后兼容性

为了保证向后兼容性，旧的配置文件（`app/config.py` 和 `env.py`）仍然可以使用，但会显示弃用警告。建议尽快迁移到新的配置系统。

## 配置验证

使用共享配置工具验证配置：

```bash
# 验证所有配置
python config/shared_config.py validate

# 显示服务URL
python config/shared_config.py urls

# 创建.env文件
python config/shared_config.py create-env
```

## 环境变量优先级

1. 系统环境变量（最高优先级）
2. `.env` 文件中的变量
3. 代码中的默认值（最低优先级）

## 安全注意事项

- 不要将 `.env` 文件提交到版本控制系统
- 在生产环境中更改默认的 SECRET_KEY
- 确保数据库连接字符串的安全性
- 定期更新微信小程序的密钥

## 故障排除

### 常见问题

1. **配置文件找不到**
   - 确保 `config/.env` 文件存在
   - 检查文件路径是否正确

2. **环境变量未生效**
   - 重启服务
   - 检查环境变量名是否正确
   - 确认 `.env` 文件格式正确

3. **服务启动失败**
   - 检查端口是否被占用
   - 验证数据库连接字符串
   - 查看日志文件

### 调试命令

```bash
# 检查配置加载情况
python -c "from config.fastapi_config import settings; print(f'Port: {settings.PORT}')"

# 检查PDF服务配置
node -e "const config = require('./config/pdf_service_config'); config.printConfig();"
```

## 更新日志

- **v1.0.0**: 初始配置重构
  - 统一环境变量管理
  - 分离FastAPI和PDF服务配置
  - 添加配置验证功能
  - 提供向后兼容性支持
