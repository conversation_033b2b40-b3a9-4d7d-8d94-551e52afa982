# 证件照尺寸信息更新说明

## 更新内容

### 1. 尺寸像素值修正

根据标准证件照规格，更新了以下尺寸的像素值：

| 尺寸类型 | 更新前像素 | 更新后像素 | 冲印尺寸 |
|----------|------------|------------|----------|
| 一寸 | 295x413 | 295x413 | 2.5cm*3.5cm |
| 二寸 | 413x579 | 413x579 | 3.5cm*4.9cm |
| 大一寸 | 390x567 | 390x567 | 3.3cm*4.8cm |
| **小一寸** | **260x378** | **259x377** | 2.2cm*3.2cm |
| 大二寸 | 413x626 | 413x626 | 3.5cm*5.3cm |
| 小二寸 | 413x531 | 413x531 | 3.5cm*4.5cm |

**主要变更**：
- 小一寸像素从 `260x378` 修正为 `259x377`
- 二寸显示名称从 "两寸" 更正为 "二寸"

### 2. 新增冲印尺寸属性

在获取尺寸列表API (`GET /idphoto/sizes`) 中新增 `print_size` 字段，提供物理冲印尺寸信息。

#### API响应变化

**更新前**：
```json
{
  "name": "一寸",
  "value": "one_inch",
  "width": 295,
  "height": 413,
  "description": "标准一寸证件照"
}
```

**更新后**：
```json
{
  "name": "一寸",
  "value": "one_inch", 
  "width": 295,
  "height": 413,
  "print_size": "2.5cm*3.5cm",
  "description": "标准一寸证件照"
}
```

## 技术实现

### 1. 配置文件更新

#### app/config.py
```python
IDPHOTO_SIZES = {
    "one_inch": {"width": 295, "height": 413},
    "two_inch": {"width": 413, "height": 579},
    "big_one_inch": {"width": 390, "height": 567},
    "small_one_inch": {"width": 259, "height": 377},  # 修正像素值
    "big_two_inch": {"width": 413, "height": 626},
    "small_two_inch": {"width": 413, "height": 531}
}
```

#### app/schemas/idphoto.py
```python
IDPHOTO_SIZE_CONFIG = {
    IDPhotoSize.ONE_INCH: {
        "name": "一寸",
        "width": 295,
        "height": 413,
        "print_size": "2.5cm*3.5cm",  # 新增冲印尺寸
        "description": "标准一寸证件照"
    },
    # ... 其他尺寸配置
}
```

### 2. API路由更新

#### app/routers/idphoto.py
```python
@router.get("/sizes")
async def get_available_sizes():
    from app.schemas.idphoto import IDPHOTO_SIZE_CONFIG
    
    sizes = []
    for size_enum, config in IDPHOTO_SIZE_CONFIG.items():
        sizes.append({
            "name": config["name"],
            "value": size_enum.value,
            "width": config["width"],
            "height": config["height"],
            "print_size": config["print_size"],  # 新增字段
            "description": config["description"]
        })
    
    return {
        "success": True,
        "message": "获取尺寸列表成功",
        "data": {"sizes": sizes}
    }
```

## 客户端适配指南

### 1. 响应数据处理

客户端现在可以获取到冲印尺寸信息：

```javascript
// 获取尺寸列表
wx.request({
  url: 'https://your-api.com/idphoto/sizes',
  method: 'GET',
  success: function(res) {
    const sizes = res.data.data.sizes;
    sizes.forEach(size => {
      console.log(`${size.name}: ${size.width}x${size.height}像素, ${size.print_size}`);
      // 输出: 一寸: 295x413像素, 2.5cm*3.5cm
    });
  }
});
```

### 2. UI显示建议

```javascript
// 在尺寸选择界面显示完整信息
const sizeOptions = sizes.map(size => ({
  label: `${size.name} (${size.print_size})`,
  value: size.value,
  detail: `${size.width}x${size.height}像素`
}));

// 示例显示效果：
// 一寸 (2.5cm*3.5cm)
// 295x413像素
```

### 3. 向后兼容性

此次更新完全向后兼容：
- 原有的 `width`、`height`、`name`、`value` 字段保持不变
- 新增的 `print_size` 字段为可选使用
- 客户端可以选择性地使用新字段来增强用户体验

## 测试验证

### 验证工具输出

运行 `python verify_idphoto_setup.py` 可以看到：

```
详细尺寸信息（包含冲印尺寸）:
  - 一寸: 295x413像素, 2.5cm*3.5cm
  - 二寸: 413x579像素, 3.5cm*4.9cm
  - 大一寸: 390x567像素, 3.3cm*4.8cm
  - 小一寸: 259x377像素, 2.2cm*3.2cm
  - 大二寸: 413x626像素, 3.5cm*5.3cm
  - 小二寸: 413x531像素, 3.5cm*4.5cm
```

### API测试输出

运行 `python test_idphoto_api.py` 可以看到：

```
支持的尺寸数量: 6
  - 一寸: 295x413 (2.5cm*3.5cm)
  - 二寸: 413x579 (3.5cm*4.9cm)
  - 大一寸: 390x567 (3.3cm*4.8cm)
  - 小一寸: 259x377 (2.2cm*3.2cm)
  - 大二寸: 413x626 (3.5cm*5.3cm)
  - 小二寸: 413x531 (3.5cm*4.5cm)
```

## 总结

本次更新主要完成了：

1. ✅ **像素值修正**：修正了小一寸的像素尺寸
2. ✅ **名称规范**：统一使用"二寸"而非"两寸"
3. ✅ **功能增强**：新增冲印尺寸信息，方便用户了解实际打印尺寸
4. ✅ **向后兼容**：保持API结构兼容性，不影响现有客户端
5. ✅ **文档更新**：同步更新API文档和测试脚本

这些更新提升了证件照API的准确性和用户体验，为用户提供了更完整的尺寸信息。
