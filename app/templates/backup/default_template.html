<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ resume.basicInfo.name }}的简历</title>
    <style>
        body {
            font-family: "Microsoft YaHei", Arial, sans-serif;
            margin: 0;
            padding: 0;
            color: #333;
            background-color: #f9f9f9;
        }
        .container {
            max-width: 800px;
            margin: 20px auto;
            padding: 30px;
            background-color: #fff;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #2c3e50;
            padding-bottom: 20px;
        }
        .personal-info {
            flex: 1;
        }
        .photo {
            width: 120px;
            height: 160px;
            margin-left: 20px;
            border: 1px solid #ddd;
        }
        h1 {
            color: #2c3e50;
            font-size: 28px;
            margin: 0 0 10px;
        }
        h2 {
            color: #2c3e50;
            font-size: 20px;
            margin: 20px 0 10px;
            padding-bottom: 5px;
            border-bottom: 1px solid #eee;
        }
        .section {
            margin-bottom: 25px;
        }
        .info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
        }
        .info-item {
            display: flex;
        }
        .info-label {
            font-weight: bold;
            min-width: 70px;
        }
        .timeline-item {
            margin-bottom: 15px;
            padding-bottom: 15px;
            border-bottom: 1px dashed #eee;
        }
        .timeline-header {
            display: flex;
            justify-content: space-between;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .timeline-content {
            margin-top: 5px;
            line-height: 1.5;
        }
        .skills-list, .awards-list, .interests-list {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 10px;
        }
        .skill-tag, .award-tag, .interest-tag {
            background-color: #f0f7ff;
            color: #0366d6;
            padding: 5px 10px;
            border-radius: 3px;
            font-size: 14px;
        }
        .evaluation {
            line-height: 1.6;
            font-style: italic;
            color: #555;
            padding: 10px;
            background-color: #f9f9f9;
            border-left: 3px solid #2c3e50;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 基本信息部分 -->
        <div class="header">
            <div class="personal-info">
                <h1>{{ resume.basicInfo.name }}</h1>
                <div class="info-grid">
                    {% if resume.basicInfo.phone %}
                    <div class="info-item">
                        <div class="info-label">电话：</div>
                        <div>{{ resume.basicInfo.phone }}</div>
                    </div>
                    {% endif %}
                    
                    {% if resume.basicInfo.email %}
                    <div class="info-item">
                        <div class="info-label">邮箱：</div>
                        <div>{{ resume.basicInfo.email }}</div>
                    </div>
                    {% endif %}
                    
                    {% if resume.basicInfo.gender %}
                    <div class="info-item">
                        <div class="info-label">性别：</div>
                        <div>{{ resume.basicInfo.gender }}</div>
                    </div>
                    {% endif %}
                    
                    {% if resume.basicInfo.age %}
                    <div class="info-item">
                        <div class="info-label">年龄：</div>
                        <div>{{ resume.basicInfo.age }}</div>
                    </div>
                    {% endif %}
                    
                    {% if resume.basicInfo.city %}
                    <div class="info-item">
                        <div class="info-label">所在城市：</div>
                        <div>{{ resume.basicInfo.city }}</div>
                    </div>
                    {% endif %}
                    
                    {% if resume.basicInfo.wechat %}
                    <div class="info-item">
                        <div class="info-label">微信：</div>
                        <div>{{ resume.basicInfo.wechat }}</div>
                    </div>
                    {% endif %}
                    
                    {% if resume.basicInfo.customTitle1 and resume.basicInfo.customContent1 %}
                    <div class="info-item">
                        <div class="info-label">{{ resume.basicInfo.customTitle1 }}：</div>
                        <div>{{ resume.basicInfo.customContent1 }}</div>
                    </div>
                    {% endif %}
                    
                    {% if resume.basicInfo.customTitle2 and resume.basicInfo.customContent2 %}
                    <div class="info-item">
                        <div class="info-label">{{ resume.basicInfo.customTitle2 }}：</div>
                        <div>{{ resume.basicInfo.customContent2 }}</div>
                    </div>
                    {% endif %}
                </div>
            </div>
            
            {% if resume.basicInfo.photoUrl %}
            <img src="data:image/jpeg;base64,{{ resume.basicInfo.photoUrl }}" alt="照片" class="photo">
            {% endif %}
        </div>

        <!-- 求职意向 -->
        {% if resume.jobIntention %}
        <div class="section">
            <h2>{{ resume.jobIntention.title }}</h2>
            <div class="info-grid">
                {% if resume.jobIntention.position %}
                <div class="info-item">
                    <div class="info-label">期望职位：</div>
                    <div>{{ resume.jobIntention.position }}</div>
                </div>
                {% endif %}
                
                {% if resume.jobIntention.city %}
                <div class="info-item">
                    <div class="info-label">期望城市：</div>
                    <div>{{ resume.jobIntention.city }}</div>
                </div>
                {% endif %}
                
                {% if resume.jobIntention.salary %}
                <div class="info-item">
                    <div class="info-label">期望薪资：</div>
                    <div>{{ resume.jobIntention.salary }}</div>
                </div>
                {% endif %}
                
                {% if resume.jobIntention.status %}
                <div class="info-item">
                    <div class="info-label">求职状态：</div>
                    <div>{{ resume.jobIntention.status }}</div>
                </div>
                {% endif %}
            </div>
        </div>
        {% endif %}

        <!-- 教育经历 -->
        {% if resume.education and resume.education|length > 0 %}
        <div class="section">
            <h2>教育经历</h2>
            {% for edu in resume.education %}
            <div class="timeline-item">
                <div class="timeline-header">
                    <div>{{ edu.school }} | {{ edu.major }} | {{ edu.degree }}</div>
                    <div>{{ edu.start_date }} ~ {{ edu.end_date }}</div>
                </div>
                {% if edu.description %}
                <div class="timeline-content">
                    {{ edu.description }}
                </div>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        {% endif %}

        <!-- 工作经验 -->
        {% if resume.work and resume.work|length > 0 %}
        <div class="section">
            <h2>工作经验</h2>
            {% for work in resume.work %}
            <div class="timeline-item">
                <div class="timeline-header">
                    <div>{{ work.company }} | {{ work.position }}</div>
                    <div>{{ work.start_date }} ~ {{ work.end_date }}</div>
                </div>
                {% if work.description %}
                <div class="timeline-content">
                    {{ work.description }}
                </div>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        {% endif %}

        <!-- 项目经验 -->
        {% if resume.project and resume.project|length > 0 %}
        <div class="section">
            <h2>项目经验</h2>
            {% for project in resume.project %}
            <div class="timeline-item">
                <div class="timeline-header">
                    <div>{{ project.name }} | {{ project.role }}</div>
                    <div>{{ project.start_date }} ~ {{ project.end_date }}</div>
                </div>
                {% if project.description %}
                <div class="timeline-content">
                    {{ project.description }}
                </div>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        {% endif %}

        <!-- 技能 -->
        {% if resume.skills and resume.skills|length > 0 %}
        <div class="section">
            <h2>技能特长</h2>
            <div class="skills-list">
                {% for skill in resume.skills %}
                <div class="skill-tag">{{ skill }}</div>
                {% endfor %}
            </div>
        </div>
        {% endif %}

        <!-- 获奖情况 -->
        {% if resume.awards and resume.awards|length > 0 %}
        <div class="section">
            <h2>获奖情况</h2>
            <div class="awards-list">
                {% for award in resume.awards %}
                <div class="award-tag">{{ award }}</div>
                {% endfor %}
            </div>
        </div>
        {% endif %}

        <!-- 兴趣爱好 -->
        {% if resume.interests and resume.interests|length > 0 %}
        <div class="section">
            <h2>兴趣爱好</h2>
            <div class="interests-list">
                {% for interest in resume.interests %}
                <div class="interest-tag">{{ interest }}</div>
                {% endfor %}
            </div>
        </div>
        {% endif %}

        <!-- 自我评价 -->
        {% if resume.evaluation %}
        <div class="section">
            <h2>自我评价</h2>
            <div class="evaluation">
                {{ resume.evaluation }}
            </div>
        </div>
        {% endif %}

        <!-- 自定义模块 -->
        {% if resume.custom %}
            {% for key, custom_section in resume.custom.items() %}
            <div class="section">
                <h2>{{ custom_section.title }}</h2>
                {% for item in custom_section.content %}
                <div class="timeline-item">
                    <div class="timeline-header">
                        <div>{{ item.title }}</div>
                        {% if item.date %}
                        <div>{{ item.date }}</div>
                        {% endif %}
                    </div>
                    {% if item.description %}
                    <div class="timeline-content">
                        {{ item.description }}
                    </div>
                    {% endif %}
                </div>
                {% endfor %}
            </div>
            {% endfor %}
        {% endif %}
    </div>
</body>
</html> 