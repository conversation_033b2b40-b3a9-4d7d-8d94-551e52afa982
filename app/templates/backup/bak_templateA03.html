<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>{{ resume.basicInfo.name }}的简历</title>
  <link rel="stylesheet" href="{{ base_url|default('') }}/static/fontawesome/css/all.min.css">
  <style>
    :root {
      --theme-color: {{ theme_color|default('#32568c') }}; /* Adjusted blue based on image */
      --base-font-size: {{ base_font_size|default(10) }}pt; /* Adjusted base size slightly */
      --max-font-size: {{ max_font_size|default(12) }}pt;
      --spacing: {{ spacing|default(1.5) }}; /* Increased line spacing slightly */
      --text-color: #333333; /* Dark gray for text */
      --secondary-text-color: #555555;
      --border-color: #e0e0e0;
    }

    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", <PERSON><PERSON>, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
    }

    body {
      background-color: #f0f0f0; /* Light gray background for contrast */
      display: flex;
      justify-content: center;
      padding: 10px 0;
      overflow-x: hidden;
    }

    .resume-container {
      width: 210mm;
      min-height: 297mm;
      background-color: white;
      box-shadow: 0 0 15px rgba(0, 0, 0, 0.15);
      display: flex;
      flex-direction: column;
      overflow: hidden;
      font-size: var(--base-font-size);
      line-height: var(--spacing);
      color: var(--text-color);
    }

    /* Header Section */
    .resume-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      background-color: var(--theme-color);
      padding: 15px 30px;
      color: white;
    }

    .resume-header-title {
      font-size: calc(var(--base-font-size) * 2);
      font-weight: bold;
      letter-spacing: 2px;
    }

    .resume-header-icons {
      display: flex;
      gap: 20px;
    }

    .resume-header-icons i {
      font-size: calc(var(--base-font-size) * 1.8);
    }

    /* Basic Info Section */
    .basic-info-section {
      padding: 20px 30px;
      display: grid;
      grid-template-columns: 1fr auto; /* Main content | Photo */
      gap: 25px;
      border-bottom: 1px solid var(--border-color);
      align-items: start;
    }

    .basic-info-left {
      display: flex;
      flex-direction: column;
      gap: 15px;
    }

    .basic-info-name h1 {
      font-size: calc(var(--base-font-size) * 2.2);
      font-weight: bold;
      margin-bottom: 10px;
      color: #000000;
    }

    .basic-info-details {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); /* Responsive columns */
      gap: 8px 15px; /* Row and column gap */
      font-size: var(--base-font-size);
      color: var(--secondary-text-color);
    }

    .basic-info-details p {
      display: flex;
      align-items: baseline;
    }

    .basic-info-details p span:first-child {
      font-weight: bold;
      width: 70px; /* Fixed width for labels */
      flex-shrink: 0;
      color: var(--text-color);
      display: inline-block;
      text-align: right; /* Align labels to the right for consistency */
      margin-right: 8px;
    }
     .basic-info-details p span:last-child {
       word-break: break-all;
     }

    .basic-info-photo {
      width: 110px; /* Slightly smaller photo */
      height: 154px; /* Maintain aspect ratio (e.g., 7:5) */
      object-fit: cover;
      border: 1px solid var(--border-color);
      align-self: center; /* Vertically center photo */
    }

    /* General Section Styling */
    .section {
      padding: 15px 30px;
      margin-bottom: 0px; /* Reduced margin */
    }

    .section-header {
      display: flex;
      align-items: center;
      background-color: var(--theme-color);
      color: white;
      padding: 6px 15px; /* Reduced padding */
      margin-bottom: 15px;
      border-radius: 3px;
    }

    .section-header i {
      font-size: calc(var(--base-font-size) * 1.3);
      margin-right: 12px;
      width: 20px; /* Fixed width for icon alignment */
      text-align: center;
    }

    .section-header h2 {
      font-size: calc(var(--base-font-size) * 1.3);
      font-weight: bold;
    }

    .section-content {
      padding-left: 5px; /* Small indent for content */
    }

    /* Specific Section Content Styling */
    .education-item, .work-item, .project-item, .internship-item, .custom-item {
      margin-bottom: 15px;
    }

    .item-header {
      display: flex;
      justify-content: space-between;
      flex-wrap: wrap;
      font-weight: bold;
      margin-bottom: 5px;
      gap: 10px;
    }

    .item-header .date-range {
      color: var(--secondary-text-color);
      font-weight: normal;
    }

    .item-description p {
      margin-bottom: 5px;
      color: var(--secondary-text-color);
       padding-left: 15px; /* Indent description */
    }
    .item-description p strong {
        color: var(--text-color);
        margin-right: 5px;
    }


    /* List style for experience, awards, interests */
    .bullet-list {
      list-style: none;
      padding-left: 0;
    }

    .bullet-list li {
      margin-bottom: 8px;
      position: relative;
      padding-left: 20px;
      line-height: var(--spacing);
    }

    .bullet-list li::before {
      content: "";
      position: absolute;
      left: 5px;
      top: calc(var(--base-font-size) * var(--spacing) / 2 - 3px); /* Center the dot vertically */
      width: 6px;
      height: 6px;
      background-color: var(--theme-color);
      border-radius: 50%;
    }

     .bullet-list li .date-prefix {
         font-weight: bold;
         margin-right: 8px;
     }


    /* Skills Section Specifics */
    .skills-section-content {
        padding-left: 5px;
    }
    .skills-subsection {
        margin-bottom: 15px;
    }
     .skills-subsection h3 {
         font-size: var(--base-font-size);
         font-weight: bold;
         margin-bottom: 8px;
         color: var(--text-color);
     }
    .certificates-list p {
        margin-bottom: 5px;
        padding-left: 15px; /* Indent certificates */
    }
     .personal-skills-description p {
         color: var(--secondary-text-color);
         line-height: var(--spacing);
         padding-left: 15px; /* Indent description */

     }


    /* Evaluation Section Specifics */
    .evaluation-list {
      list-style-type: decimal; /* Use numbers for ordered list */
      padding-left: 25px; /* Standard indent for ordered list */
    }
     .evaluation-list li {
       margin-bottom: 8px;
     }


    /* Hide empty sections */
    .hidden {
      display: none;
    }

    /* Print Styles */
    @page {
      size: A4;
      margin: 0;
    }

    @media print {
      body {
        background-color: white;
        padding: 0;
      }
      .resume-container {
        box-shadow: none;
        margin: 0;
        width: 100%;
        min-height: 0;
      }
      .section {
        padding: 10px 20px; /* Adjust padding for print */
      }
      .basic-info-section {
         padding: 15px 20px;
      }
       .section-header {
         padding: 4px 10px;
         margin-bottom: 10px;
          /* Ensure headers don't break across pages */
         break-inside: avoid;
         page-break-inside: avoid;
       }
       .education-item, .work-item, .project-item, .internship-item, .custom-item, .bullet-list li, .evaluation-list li {
           /* Prevent items from breaking mid-item */
          break-inside: avoid;
          page-break-inside: avoid;
       }
        .item-description p {
             orphans: 3;
             widows: 3;
        }

    }

     /* Ensure content wraps */
    p, h1, h2, h3, span, li, div {
       word-wrap: break-word;
       overflow-wrap: break-word;
       max-width: 100%; /* Prevent overflow */
    }

  </style>
</head>
<body>
  <div class="resume-container">

    <!-- Header -->
    <header class="resume-header">
      <span class="resume-header-title">个人简历</span>
      <div class="resume-header-icons">
        <i class="fas fa-building"></i>
        <i class="fas fa-graduation-cap"></i>
        <i class="fas fa-seedling"></i>
      </div>
    </header>

    <!-- Basic Info -->
    <section class="basic-info-section">
      <div class="basic-info-left">
        <div class="basic-info-name">
          <h1>{{ resume.basicInfo.name | default('姓名') }}</h1>
        </div>
        <div class="basic-info-details">
          {% if resume.basicInfo.nation %}<p><span>民族:</span><span>{{ resume.basicInfo.nation }}</span></p>{% endif %}
          {% if resume.jobIntention.position %}<p><span>求职意向:</span><span>{{ resume.jobIntention.position }}</span></p>{% endif %}
          {% if resume.basicInfo.hometown %}<p><span>籍贯:</span><span>{{ resume.basicInfo.hometown }}</span></p>{% endif %}
          {% if resume.basicInfo.birthday %}<p><span>出生年月:</span><span>{{ resume.basicInfo.birthday }}</span></p>{% endif %}
          {% if resume.basicInfo.city %}<p><span>现居:</span><span>{{ resume.basicInfo.city }}</span></p>{% endif %}
          {% if resume.basicInfo.height %}<p><span>身高:</span><span>{{ resume.basicInfo.height }}</span></p>{% endif %}
          {% if resume.basicInfo.phone %}<p><span>电话:</span><span>{{ resume.basicInfo.phone }}</span></p>{% endif %}
          {% if resume.basicInfo.educationLevel %}<p><span>学历:</span><span>{{ resume.basicInfo.educationLevel }}</span></p>{% endif %}
          {% if resume.basicInfo.email %}<p><span>邮箱:</span><span>{{ resume.basicInfo.email }}</span></p>{% endif %}
          {% if resume.basicInfo.politics %}<p><span>政治面貌:</span><span>{{ resume.basicInfo.politics }}</span></p>{% endif %}
          {# Add other fields like wechat, gender, age, marriage, weight if needed #}
          {% if resume.basicInfo.wechat %}<p><span>微信:</span><span>{{ resume.basicInfo.wechat }}</span></p>{% endif %}
          {% if resume.basicInfo.gender %}<p><span>性别:</span><span>{{ resume.basicInfo.gender }}</span></p>{% endif %}
          {% if resume.basicInfo.age %}<p><span>年龄:</span><span>{{ resume.basicInfo.age }}</span></p>{% endif %}
          {% if resume.basicInfo.marriage %}<p><span>婚姻:</span><span>{{ resume.basicInfo.marriage }}</span></p>{% endif %}
          {% if resume.basicInfo.weight %}<p><span>体重:</span><span>{{ resume.basicInfo.weight }}</span></p>{% endif %}
          {# Custom Fields #}
          {% if resume.basicInfo.customTitle1 and resume.basicInfo.customContent1 %}<p><span>{{ resume.basicInfo.customTitle1 }}:</span><span>{{ resume.basicInfo.customContent1 }}</span></p>{% endif %}
          {% if resume.basicInfo.customTitle2 and resume.basicInfo.customContent2 %}<p><span>{{ resume.basicInfo.customTitle2 }}:</span><span>{{ resume.basicInfo.customContent2 }}</span></p>{% endif %}
        </div>
      </div>
      <img class="basic-info-photo" src="{{ resume.basicInfo.photoUrl }}" alt="个人照片" loading="eager" onerror="this.style.display='none';">
    </section>

    {# --- 修改开始：遍历排序后的模块 --- #}
    {% for module in ordered_modules %}
      {% set key = module.key %}
      {% set title = module.title %}
      {% set icon = module.icon %}
      {% set data = module.data %}
      {% set is_hidden = hide_flags.get('hide_' ~ key, True) %} {# 获取隐藏标志 #}

      {# ---- 教育背景 ---- #}
      {% if key == 'education' %}
        <section class="section {% if is_hidden %}hidden{% endif %}" id="education-section">
          <div class="section-header">
            <i class="fas {{ icon }}"></i>
            <h2>{{ title }}</h2>
          </div>
          <div class="section-content">
            {% for edu in data %}
              <div class="education-item">
                <div class="item-header">
                  <span>{{ edu.school | default('') }}</span>
                  <span>{{ edu.major | default('') }} ({{ edu.degree | default('') }})</span>
                  <span class="date-range">{{ edu.startDate | default('') }} - {{ edu.endDate | default('') }}</span>
                </div>
                {% if edu.description %}
                  <div class="item-description">
                    <p><strong>主修课程:</strong> {{ edu.description }}</p>
                  </div>
                {% endif %}
              </div>
            {% endfor %}
          </div>
        </section>

      {# ---- 在校经历 ---- #}
      {% elif key == 'school' %}
        <section class="section {% if is_hidden %}hidden{% endif %}" id="school-experience-section">
          <div class="section-header">
            <i class="fas {{ icon }}"></i>
            <h2>{{ title }}</h2>
          </div>
          <div class="section-content">
            <ul class="bullet-list">
              {% for exp in data %}
                <li><span class="date-prefix">{{ exp.startDate | default('') }}:</span>{{ exp.content | default('') }}</li>
              {% endfor %}
            </ul>
          </div>
        </section>

      {# ---- 实习经历 ---- #}
      {% elif key == 'internship' %}
         <section class="section {% if is_hidden %}hidden{% endif %}" id="internship-section">
           <div class="section-header">
             <i class="fas {{ icon }}"></i>
             <h2>{{ title }}</h2>
           </div>
           <div class="section-content">
             {% for intern in data %}
               <div class="internship-item">
                 <div class="item-header">
                   <span>{{ intern.company | default('') }} - {{ intern.position | default('') }}</span>
                   <span class="date-range">{{ intern.startDate | default('') }} - {{ intern.endDate | default('') }}</span>
                 </div>
                 <div class="item-description">
                    <p>{{ intern.content | default('') }}</p>
                 </div>
               </div>
             {% endfor %}
           </div>
         </section>

      {# ---- 工作经历 ---- #}
      {% elif key == 'work' %}
        <section class="section {% if is_hidden %}hidden{% endif %}" id="work-section">
          <div class="section-header">
            <i class="fas {{ icon }}"></i>
            <h2>{{ title }}</h2>
          </div>
          <div class="section-content">
            {% for job in data %}
              <div class="work-item">
                <div class="item-header">
                  <span>{{ job.company | default('') }} - {{ job.position | default('') }}</span>
                  <span class="date-range">{{ job.startDate | default('') }} - {{ job.endDate | default('') }}</span>
                </div>
                 {% if job.description %}
                  <div class="item-description">
                    <p>{{ job.description }}</p>
                  </div>
                {% endif %}
              </div>
            {% endfor %}
          </div>
        </section>

      {# ---- 项目经历 ---- #}
      {% elif key == 'project' %}
        <section class="section {% if is_hidden %}hidden{% endif %}" id="project-section">
          <div class="section-header">
            <i class="fas {{ icon }}"></i>
            <h2>{{ title }}</h2>
          </div>
          <div class="section-content">
            {% for proj in data %}
              <div class="project-item">
                <div class="item-header">
                  <span>{{ proj.projectName | default('') }} {% if proj.role %}({{ proj.role }}){% endif %}</span>
                  <span class="date-range">{{ proj.startDate | default('') }} - {{ proj.endDate | default('') }}</span>
                </div>
                {% if proj.description %}
                  <div class="item-description">
                     <p>{{ proj.description }}</p>
                  </div>
                {% endif %}
              </div>
            {% endfor %}
          </div>
        </section>

      {# ---- 技能证书 ---- #}
      {% elif key == 'skills' %}
        <section class="section {% if is_hidden %}hidden{% endif %}" id="skills-section">
          <div class="section-header">
            <i class="fas {{ icon }}"></i>
            <h2>{{ title }}</h2>
          </div>
          <div class="skills-section-content">
             <div class="skills-subsection certificates-list">
                 <h3>获得证书</h3>
                 {% for skill in data %}
                   <p>{{ skill }}</p>
                 {% endfor %}
             </div>
             <div class="skills-subsection personal-skills-description">
                  <h3>个人技能</h3>
                  {# Placeholder text - Link this to a custom field or evaluation item if needed #}
                  <p>具备出色的语言表达能力；拥有敏锐的观察力和丰富的想象力与创造力；注重细节，能够专注地处理各项任务，保持冷静与稳定。</p>
             </div>
          </div>
        </section>

      {# ---- 奖项荣誉 ---- #}
      {% elif key == 'awards' %}
        <section class="section {% if is_hidden %}hidden{% endif %}" id="awards-section">
          <div class="section-header">
            <i class="fas {{ icon }}"></i>
            <h2>{{ title }}</h2>
          </div>
          <div class="section-content">
            <ul class="bullet-list">
              {% for award in data %}
                <li>{{ award }}</li>
              {% endfor %}
            </ul>
          </div>
        </section>

      {# ---- 兴趣爱好 ---- #}
      {% elif key == 'interests' %}
        <section class="section {% if is_hidden %}hidden{% endif %}" id="interests-section">
          <div class="section-header">
            <i class="fas {{ icon }}"></i>
            <h2>{{ title }}</h2>
          </div>
          <div class="section-content">
            <ul class="bullet-list">
              {% for interest in data %}
                <li>{{ interest }}</li>
              {% endfor %}
            </ul>
          </div>
        </section>

      {# ---- 自我评价 ---- #}
      {% elif key == 'evaluation' %}
        <section class="section {% if is_hidden %}hidden{% endif %}" id="self-evaluation-section">
          <div class="section-header">
            <i class="fas {{ icon }}"></i>
            <h2>{{ title }}</h2>
          </div>
          <div class="section-content">
             {# Adapting to list of dicts or list of strings #}
             {% if data and data|length > 0 and data[0] is mapping %}
                 <ol class="evaluation-list">
                   {% for eval_item in data %}
                     <li>{{ eval_item.content | default('') }}</li>
                   {% endfor %}
                 </ol>
             {% elif data and data|length > 0 and data[0] is string %}
                  <ul class="bullet-list">
                    {% for eval_item in data %}
                       <li>{{ eval_item }}</li>
                    {% endfor %}
                  </ul>
             {% elif data is string %} {# Handle if evaluation is just a single string #}
                <p>{{ data }}</p>
             {% endif %}
          </div>
        </section>

      {# ---- 自定义模块 ---- #}
      {% elif key in ['custom1', 'custom2', 'custom3'] %}
         {% if data %} {# Only render custom section if data exists #}
          <section class="section {% if is_hidden %}hidden{% endif %}" id="{{ key }}-section">
            <div class="section-header">
              <i class="fas {{ icon }}"></i>
              {# Try to get name from first item, fallback to default title #}
              <h2>{{ (data[0].customName if data and data[0] and data[0].customName else title) }}</h2>
            </div>
            <div class="section-content">
              {% for item in data %}
                <div class="custom-item">
                   <div class="item-header">
                     <span>{{ item.customName | default('') }} {% if item.role %}({{ item.role }}){% endif %}</span>
                     <span class="date-range">{{ item.startDate | default('') }} - {{ item.endDate | default('') }}</span>
                   </div>
                   {% if item.content %}
                     <div class="item-description">
                        <p>{{ item.content }}</p>
                     </div>
                   {% endif %}
                </div>
              {% endfor %}
            </div>
          </section>
         {% endif %}
      {% endif %}
    {% endfor %}
    {# --- 修改结束 --- #}

  </div>
</body>
</html>