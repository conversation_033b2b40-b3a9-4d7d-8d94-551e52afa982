<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>简小历的简历</title>
  <link rel="stylesheet" href="{{ base_url|default('') }}/static/fontawesome/css/all.min.css">
  <style>
    :root {
      --theme-color: #2E75B6; /* Adjusted blue based on image */
      --base-font-size: 11pt; /* Adjusted base size slightly */
      --max-font-size: 12pt;
      --spacing: 1.2; /* Increased line spacing slightly */
      --text-color: #333333; /* Dark gray for text */
      --secondary-text-color: #555555;
      --border-color: #e0e0e0;
    }

    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", <PERSON><PERSON>, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
    }

    body {
      background-color: #f0f0f0; /* Light gray background for contrast */
      display: flex;
      justify-content: center;
      padding: 10px 0;
      overflow-x: hidden;
    }

    .resume-container {
      width: 210mm;
      min-height: 297mm;
      background-color: white;
      box-shadow: 0 0 15px rgba(0, 0, 0, 0.15);
      display: flex;
      flex-direction: column;
      overflow: hidden;
      font-size: var(--base-font-size);
      line-height: var(--spacing);
      color: var(--text-color);
    }

    /* Header Section */
    .resume-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      background-color: var(--theme-color);
      padding: 15px 30px;
      color: white;
    }

    .resume-header-title {
      font-size: calc(var(--base-font-size) * 2);
      font-weight: bold;
      letter-spacing: 2px;
    }

    .resume-header-icons {
      display: flex;
      gap: 20px;
    }

    .resume-header-icons i {
      font-size: calc(var(--base-font-size) * 1.8);
    }

    /* Basic Info Section */
    .basic-info-section {
      padding: 20px 30px;
      display: grid;
      grid-template-columns: 1fr auto; /* Main content | Photo */
      gap: 25px;
      border-bottom: 1px solid var(--border-color);
      align-items: start;
    }

    .basic-info-left {
      display: flex;
      flex-direction: column;
      gap: 15px;
    }


    .basic-info-name h2 {
      display: flex;
      align-items: center;
      margin-bottom: 10px;
    }

    .basic-info-name h2 span {
        font-size: calc(var(--base-font-size) *1.8);
        font-weight: bold;
        color: #000000;
    }
    .basic-info-name h2::after {
      content: "";
      flex-grow: 1;
      height: 1px;
      margin-left: 10px;
      background-color: var(--theme-color);
    }

    .basic-info-details {
      display: grid;
      grid-template-columns: repeat(2, minmax(200px, 1fr)); /* Responsive columns */
      gap: 8px 15px; /* Row and column gap */
      font-size: var(--base-font-size);
      color: var(--secondary-text-color);
      background-color: rgb(255, 255, 255);
    }

    .basic-info-details p {
      display: flex;
      align-items: baseline;
    }

    .basic-info-details p span:first-child {
      font-weight: bold;
      min-width: 100px;
      text-align: justify;
      color: var(--text-color);
      text-align: center;
      background-color: rgb(255, 255, 255);
    }


     .basic-info-details p span:last-child {
       word-break: break-all;
     }

    .basic-info-photo {
      width: 110px; /* Slightly smaller photo */
      height: 154px; /* Maintain aspect ratio (e.g., 7:5) */
      object-fit: cover;
      border: 1px solid var(--border-color);
      align-self: center; /* Vertically center photo */
    }







    /* General Section Styling */
    .section {
      padding: 5px 25px;
      margin-bottom: 0px; /* Reduced margin */

    }

    .section-header {
      margin-bottom: 10px;
      display: flex;
      align-items: center;
/*      height: 100px;  */
      background-color: rgb(255, 255, 255);
    }

    .section-header i {
      margin-right: 10px;
      width: 18px;
      font-size: calc(var(--base-font-size) * 1.1);
    }

    .section-header h2 {
      font-weight: bold;
      font-size: calc(var(--base-font-size) * 1.3);
    }
    .section-title {
        display: flex;

        align-items: center;
        font-size: calc(var(--base-font-size) * 1.3);
        background-color: var(--theme-color);
        border-radius: 6px;
        padding: 6px 15px; /* Reduced padding */
        color: #fff;
    }
    .section-header::after{
      content: "";
      flex-grow: 1;
      height: 1px;
      margin-left: 10px;
      background-color: var(--theme-color);
    }



    .section-content {
      padding-left: 5px; /* Small indent for content */
      background-color: rgb(255, 255, 255);
    }

    /* Specific Section Content Styling */
    /*.education-item, .work-item, .project-item, .internship-item, .custom-item {
      margin-bottom: 0px;
    }*/

    .section-item {
        margin-bottom: 5px;
    }
    .item-header {
        margin-bottom: 5px;
    }


    .section-item .three-column {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 10px;
        font-weight: bold;
    }
    .section-item .two-column {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 10px;
        font-weight: bold;
    }


    /*
    .item-header {
      display: flex;
      justify-content: space-between;
      flex-wrap: wrap;
      font-weight: bold;
      margin-bottom: 5px;
      gap: 10px;
    }
    */

    .item-header .date-range {
      color: var(--secondary-text-color);
      font-weight: normal;
    }

    .item-description p {
      color: var(--secondary-text-color);
       padding-left: 15px; /* Indent description */
    }
    .item-description p strong {
        color: var(--text-color);
        margin-right: 5px;
    }


    /* List style for experience, awards, interests */
    /*.bullet-list {
      list-style: none;
      padding-left: 0;
    }

    .bullet-list li {
      margin-bottom: 8px;
      position: relative;
      padding-left: 20px;
      line-height: var(--spacing);
    }

    .bullet-list li::before {
      content: "";
      position: absolute;
      left: 5px;
      top: calc(var(--base-font-size) * var(--spacing) / 2 - 3px); /* Center the dot vertically
      width: 6px;
      height: 6px;
      background-color: var(--theme-color);
      border-radius: 50%;
    }

     .bullet-list li .date-prefix {
         font-weight: bold;
         margin-right: 8px;
     }
    */

    .horizon-item-list {
        padding-left: 20px;
        display: flex;
        align-items: center;
        flex-wrap:wrap;
        /*margin-right: 25px; */
        gap: 30px;
    }




    /* Hide empty sections */
    .hidden {
      display: none;
    }

    /* Print Styles */
    @page {
      size: A4;
      margin: 0;
    }

    @media print {
      body {
        background-color: white;
        padding: 0;
      }
      .resume-container {
        box-shadow: none;
        margin: 0;
        width: 100%;
        min-height: 0;
      }
      .section {
        padding: 10px 20px; /* Adjust padding for print */
      }
      .basic-info-section {
         padding: 15px 20px;
      }
       .section-header {
         padding: 4px 10px;
         margin-bottom: 10px;
          /* Ensure headers don't break across pages */
         break-inside: avoid;
         page-break-inside: avoid;
       }
       .education-item, .work-item, .project-item, .internship-item, .custom-item, .evaluation-list li {
           /* Prevent items from breaking mid-item */
          break-inside: avoid;
          page-break-inside: avoid;
       }
        .item-description p {
             orphans: 3;
             widows: 3;
        }

    }

     /* Ensure content wraps */
    p, h1, h2, h3, span, li, div {
       word-wrap: break-word;
       overflow-wrap: break-word;
       max-width: 100%; /* Prevent overflow */
    }


  </style>
</head>
<body>
  <div class="resume-container">

    <!-- Header -->
    <header class="resume-header">
      <span class="resume-header-title">个人简历</span>
      <div class="resume-header-icons">
        <i class="fas fa-building"></i>
        <i class="fas fa-graduation-cap"></i>
        <i class="fas fa-seedling"></i>
      </div>
    </header>

    <!-- Basic Info -->
    <section class="basic-info-section">
      <div class="basic-info-left">



        <div class="basic-info-name">
            <h2>
                <span>简小历</span>
            </h2>

        </div>


        <div class="basic-info-details">
          <p><span>民族:</span><span>汉</span></p>
          <p><span>求职意向:</span><span>希望职位</span></p>

          <p><span>现居:</span><span>上海</span></p>
          <p><span>电话:</span><span>1233213</span></p>
          <p><span>邮箱:</span><span><EMAIL></span></p>
          <p><span>性别:</span><span>男</span></p>
          <p><span>年龄:</span><span>30</span></p>
          <p><span>搞钱:</span><span>多钱</span></p>
          <p><span>搞钱:</span><span>多钱</span></p>
          <p><span>xxx搞 钱:</span><span>多钱</span></p>

        </div>
      </div>
      <img class="basic-info-photo" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAI8AAACUCAYAAACqcnrLAAAAAXNSR0IArs4c6QAAIABJREFUeF7tXXmYHFW1/92q3maSTDKZ7JkkQzbCAAFEWcQni6IsRn0gix8g+OFTwiKyBEXgoUb0AQGVTXgBZfP7EAV54osoKstDeQ+MIQkmAUKY7JNlMpl9pqur7vvOrVs9PT3dXbeW7unJdP1BSPrWrVv3/urcc8/yO6ynp4ejclVmwMcMsAp4fMxa5RYxAxXwVIDgewYq4PE9dZUbK+CpYMD3DFTA43vqKjdWwFPBgO8ZqIDH99RVbiw9eLRYetY1XRuwApZp2X+3kgFXRvarRQArZfdF/y/6ln93/k38XT630FOdcWfen90+83kqfULrH5fqGweeG9UHubcrHXi0GAgsZt8W8K4mRJO7YPXuTo8wFR2DSHQaeFU9kGgAaLFUFzbjPRnrhWW2u7+5bBFhVTDZGNf23Oofq2tjwqpeA84TBZsO6tPUAL0wkFmkPoSPS+UN3NsUHzxaDAw9QMvrMFqeQaxzJWI8CXBj8OhYFEkWAyKzwGtPAqv/AhCZDCGRFL84q+1PqN50M7hW5f72AJJjT4E26xsw2di8EkjnHUi9fRYShaROxtNSqIY180bwulPyjpvHo7DeOgfVyZ1K46RGhl4Ps/EuMG2KmrRU7tlfwyKCR4PO25Dc83tEd9yHmPEmuD4TDKP7R8qig0fNDXB0wjI77d8mXQ1j8rm2NCIx77Id6C3PQ9t0GXSdJrjwRc8xa84Cn/NtV/Dw1WciZnXZHdK484Cf/p3rNUjOXFoQPFo0AaxahFjfOoBlSKhcfdO/WR1IxhvBD38CnFe7zoPbu4fxexHAYy8w79kA1nQXop0r7IXMBRSVNzCbYUQPBeovgzXxDHCLAJdftKfBE5nh2jvnrd7B4/YeHsBjrbkIie6/AZr7tmmD50PgCx91nQPXFw+pQejgIXGsNb8EtvnbiFq7bEnjNuFuL2N1gLMUjMlXgdd/BZzV5N0Ohgt4SKdLvXsFRrf9UQ083EAq/hGYh99/IIJHA9MMmK0vIfLB9xAzW9QmxQ04zu9yO0uOvxA4aEle0T1cwEPbFn/vJsRbnlKbJ6sDqbGnwzrkPlgGnUYVToiqc+uzXYiSRwNrfVVIHKEbBJU2+V7IbEb71GsQm371IB2IaSa0PStsnSe0bcvW3dI6j9t7edi20LQMsZ3LAAX9TGxb484CFtwJy+j1udzh3hYOeOhY3f0utI1LbAVQZQ8P8B6kq6RmLYM58exBx/lhJXma7kd851Jl8PTVnQ8+e6nyyTPAFCvdGgp46IvHph8gsudBpS9eaWSFGnEDhj4Fqfl3gVU1DhDhwwU8pPOYzU9i1JYblMHTPuWrSDR88wCSPFoMVttrqHrvonCUY1VkkRifsthWoKUxrjjbFkB2nrC3LQKPvucZRDedC0SOcH9rsxl9U28Bn7H4wJE8dLpib12CeM+rYKzWfRLCasENJGOzYc29E0jMTkuf8CVPcXQeAg99dNUbTlQGT9fMO6BPufAAAY/UdaJrT/C3XckjOOMRX3pSWvepW5SGZPjgKZ7k8Qqe7nkPQ6s948AAjzhubnkE8e03qe3bzhJbHTCt/UgmGhGtOgJ9xg4kOl+Bro3zBCICT1fNGYjP+V7aQkzgiWy+XkkKDqWRkCQP71qDqrePUpY8BxR4SMfQ116BSN+b6kdzbqC36kPgDddBrzkyLTFYcg+szT9GfN9znvoiV0Dv4b9M+3uGE3johJpYe4gSeMzUVhgH/w5a3fEHgMJMCh8ZAtecj6jZrLbgmXpK9fws8auBsW6wbQ8jtuvnav0BoEntW/AbaGOOE3rPsNm2oIEbuxF76wi1Ld9sRs+hL4CNWngAbFskdlPboK/5gu0lV7nMZpDSF53+pdxfjxTl+vu3IJbcpAQgAZ75j6V1gfDBUySFGbYupf19gTJ4eg97GRj00alMfHHa+LfzOHv2hkvUR5Zajd7D1xeYAHuh2PvfRaTt92r6Dx1hp98GPv3L4osMHzxFUpjJOc96EV95gj1/LpZr+kiSR64Gi087QCQPKXwewdNz9OYC8Shy69p4G2L7n1UCD01qqv4O8KkXDbNtCyCdkf3jZCV3Din3yaNXl004hsC774xRH5JHfD1HrwoVPHAkTwZ4hsVpiyZfM2GtPg1Vxr7Ckkc6hY2j34DFyZY29E7RIQGPecy6AhNgSx79Pdq2futZ8pBXP3zHaPG2LQpu096+ALG+fxR+VzpoaKNgHvs6WJ85MsGD1Gokj2l2BQ/zsG1lSp7igKdYCrMM0l9/qXtAGDfQEx0PfPjlCnjyi95gksc5qvvZtnhsYl7Fn6f2g636lJJuohqGaj9MQ2TjtdDbny9s1JQhqObRz41s8Bgf3l0gXri0CjMtNHnnrbHHgUVrYaS68wJo1L5n1U6VivE8TmesaSniex8pbKGXIag46ldlYyAsuc6jum3503kuEOvhxcIsbsgMZOd5gqycAHW3QDDZn0oAvCN5tO33uAeEySjC1Px7yuaYPiTgKSvJoyZLvLXyJHk0sJ1PuPsGzWYkay+CNe/2kQ0eFYXZn+TxbufxhgrF1h7BY+5+yjUgjEwclIJkzv33kb1thS15ctl5VGOYFeHgrZlH8Ogt/+Uecy0DwVjDFSMbPCqSx8tRPdPC7PWo7g0Viq29gIeyaVv+gvj7FxZUmOkdrdkPyphtRT+i4nCDNCuphVlVYR5p4IlsPK+wczS1GsbspyvgUZE8/nSeC4S5nyzMqnaeIF9dfqOQWrqxbeaJAfv/D9F3PuMKnr65LxZMXy7Ku7h0WnLJU9F5MlZE+gcT6z9X2EhI0QgH/w0Yd2zltOVmYfa7bXkNBivK1+pF5yHiht5NiK/7dOHMk9Rq9By2qqwCwYbEzqOybY0k8FA0of7Pswq7PirgAYqtMPuSPGRhtlqEEDK5AT2HFZlrcaWAesdi7cXCTOBJrDsHjAip8liw06EsZUTsNCSSpxQ6j7LCnOHbirB4zl2MHBYR3od463+r7XIety0hed5ZnD9NW8by9B7+17KKIhwS8JTVtiV8Rp+FOf/7YPqo/AeoInrVBYnCPy/PH9MjY3n4UX90palTQ3d4rUp+2iomeLwaCYc0b8s+q4vgN77uqvwxPRXwSLQXIRgsiIW5XMCjv3dz/oB/J13psF+FJzJC6mnYS54gvq2hB48Mgn/vO4i1PpHbRSFjeazDnghpycPrpuTgKYXCrOoYLRfwRD64C/qeB/KCp7f6o8Ahy8Nb9ZB6Kjl4VHQef+6J/pAM1dNWuYAHWx/NS/LkjJG4iFTphEPChms3ZQmeUhkJhwN4hE438TLwhlvLJmvCQVUFPF55mN1CUT3ZeWydB9ufyh9NSFGEU6+HNf1KV0lQ6gYlBY+IS/nwhlAD4Idz0p8wtGWScGYSWzogdRjBpv/byJY8BJ7yTfrLT6RdFFq5DDHh5NdnSg7BVUSXtQl99Q+n06lLLV0KPa/8JI9mgNHRVTFXfbhLHiF9WC94TzO4PloULmHJ/hQgznuhV0+Fxak2RnldJQUPOUYF0UE+Bx9VxuF7oL23VJklI20kHIYsGf1QyFE6iYqkaBFRKUgIoDLhXs6EbzDwpLYhsfY8MKtH7ZMYwOiZo86VjKzTP7hZmTAqCD8PEWOTb8tT4ZKQFWa1iSvPVv7BI/0y2ltnKy80JdiJnOu59wpKuf5SSPaXJ8iiPrgXsbYVrnw1znTmYgYbTnaegbDIkkA+6o2VEmYBwAMQoaW2/kpE9v9andBS+mrY1C8hVXsstEideF+zbSViu36DSMdL6u8vwcjmLU/T6Q4ncqcB2xYsaKwNqfZ3oKeSMCMxsMRssOgk2aw8aFXC2bbIJxxNwNj+uGvS2iA0UAAWFWaLzoClj4Nm9gKpzQPrWalAyOpA59hPSTZU+7TkJd24HIyEFAQvitltfwqs5TlEzb3iPQxuikJ1vPY0oP6CgpV+VKaqGG0CSR56cVJwY28eoS55nLfILnbmpkvkeHsnn8muw6WnwaPq2xp6nUeDxlqBjcsQbXt6YCE7+b6m2Yze0SdCm3sPWHRCMTDgu89g4JHSJ7l+MUa1r1AP1fQ93IwbJe1I8Rngi0nuBCS3/wQ1O74F6I359TwnV33O0jBmLrQ+AoPHIaPW3/mqGn9NSEMXHH2TbwBmXJKWOs62pSp5hnTbymTPV6iEKOKYD/tzWWVQBAePrPupbb8P+o4fqtHCBgWQE+Myd+mAuhPDCTykL7LmX3kuXFJO+eohgIeWzM4/0jbe4s6vFxQ4RKnDW2E1PABjwumDwhS8nLaGUucRBWorxdocNGiwOv4XrOkmVCV3KJFR+sJRajX6pt0HnsdROFxOW37AQ8Xa2LzbysbaHJLksWFAE2K1vA5hIU41hQsgOp3xXnRNuBjRAjw1wwU8fkomGQfdCz7lnAMTPJkA0rbcjVjv/wBanbK1OK8kIh1HrwOf/DXwqTZ9XL7Ly7Y1pAqztNBbaxYhYWwpfFJ1qHQPfbascrdClTxS/kCLxmD2bYG25SGw/S/6L5Et63GZNYtg1n0evPZjrjsdgSf6wVVqdqeh9m1RwbbWFajedDNI/8pVm1Xod2Ynemf9KH/NDtdZKU6DIoBHDpQsp7wd2Pd36C3PCbpYmgTd7VgqMySprVn1UQg3Rs3x8ovL4UzNmpe05FGoGMzRCdNLJCFV+XHILfOtB+8Fj0xCcuZSJUoUJxgM2x6ElnzLnh+6iFyTd4mPoG/KVcD088umnrrz6sUDj60Fif9SYpvV3QR935+APU9DN96Hjn25p19vRKrmZCQnngZWfaRtVSWbiKKT0NF5DG2y0ufGx50KbdY3CkQ32kZCKg3F6U+Fy9Tj0OpvVQaPsI73NoG1/Bms9QUwsxtcrwZqT4Q5/pNgVQucL7KsogmLDJ6MmSYA0GUlhQPQatsEZnaJPPAUiwtHoBadBFY9Q6T+9nvcFVYrZxPJsO56u+NwpPYqzkenndN/5v3iBQfVe3cdgvOhaZG06UEcPkyr7DImMt+ldODJfKoDJJpmJ9iJJkqCq7+p6oKqLU/hVm7Pcvs9jDEU6mOonz94bEMDnmLPc6X/ksxABTwlmeYD8yFDAJ4c8bqZc6uoGIe+HHIrdbZRsYPm3EpDf3JWh9m6VLGf57//0oGHYn9Iv0ntEtFyvKdpwKgj0WngVfVgVVPAIuOksuh+NPf/6pKNlALIzBakereDJ7dB794lukzpUSQidUhWTYdW3RBoTHRAYJYGi05QThyTE7+UGdfEotDMbpiMAtuGRsehTA7NMpSmtcjgkRPQ2wSz/Q3oLS8i2rki5zHdxHj73/VGJGON4HWngdeeIE5f3IrKl1E5Dbm9tz0mbu2GtfdlMaZ4z6u2DYqez7KO+HwXuD5T1G/XJp6JaM2xnhZXnJrWXCTeW6PUGpdL2Lem3QhrxrUlPmnZ1ZbZxhvtNdLGgTP6eAdejEfEv9OfRQKPJqzMVscGYbvQ9j5rG8AokU3Ln1wnhil9WKa1X4R39NWeCT7+M2BjDwlYX7MfyKz1r2C7HkIstQVgo/oNf/miGbMMl8akcxCbeDqoRpdbSowT552uqZUdQZm5NvR8GfiFBXe69u0GRE+/E61v3w5E370ZEXIruRlDWbQY4NFADF1m60vgO36GROcr7lblQm9pNoNHD4Ix4WyYU8/Nn/NVcKbsMaHldfDmx2wWLpocH6GvBG4KDeW1X0Rq0gXQ6o6HZRClf26pmE4SUK3WTC6TxL/AOvLR0oInHVazRLkseciSx6ZJw7ZfIL57uYi7YYwKqga8pDQinhrecJ3HaDoJnK2PIrb3SbDUbnfp5zZcKYlS+lwYDddArz05r+tAgGfNFYh0v6D2XEmyiYVPFbR6uw3R++92SE3k/euVI0JDBI+9LVBEYWzXz+2x+/myC7211YGe2DToC38Ni01U0wlIUd96N6K77i1MlO19toUzk7z9bMYSmHWLcvbgGTyyl+6590Ab+zG1d/Qz9hz3sJ2/QGzn95Q/+JDAk/F177pD+eG+3tnqAEkg1nivUjoK2/4I4juuBCJH+Hqc200kXelKzVqWs7CI523LKYM9+SqYs68B61M7+biNs/Dv/cqy2NLd9FLZWXjgaX0VsQ8uDf/rzvHWTvB7wXheCndoew3VG04sGnDSQ5MSyFrwOFA9f4C08AweeWhIxmZDZIZk9RcMJPnvZq2v2evnQc0IATw2aomIOppc4+nhvifCYQjNN7ny5FCQHNv3w/PcSPlVo04dxB3oCzwSQH2TLweffmmRbT526W9zw7XCZFFa8EidomSZExlrZ8y8CWbd5waddGjBeNP9iO96IHy9qxDoiMhhzpMDQjH86jxO5WXzoO8DNUcrevx9fBEUd7X95+51TnPpSD09PdzHI+Ut9ukqtvKIkmxXA8YpowCted/CwCo6/Zkc0eQbnr4k//Mg7+QGuqoORqTxZ2mblG/JI7NE+qo+Dn3B3TD1utCVZxqb2f4WqtbL0F6PB5xg25YTRvnuIm96hVQKacopaCtGEXoyak55AZ243oP/c8DRPZ0Ptfk73qWOY8CjKD4v5bCdQcv7e+c+nK6NFQQ8YvfirSLa0Zx3a7jlA2TSof7OFeosJ1mLEwg8NDGUajy67Y/KGrojjlF/GTD+JOHvYbwT2PaEfcRXRb801mHO4zDGfyItCUUth013qDOLORNidcCIkA9rBhAfjZTRhnhyI2Du8CS9aLGNyVcBDdcLI19Q8NhHudXoHX0h+Nwfynz1AH4vEZWZhNXxBqKbvhsoyyUQeET87arPekZu1/RrZTC3tMzKeOdI0z3Q9z6mDkRJFtWfcuxsWUvyV5HJFm3O0XjsubAmniXcIFZsDLRkB3jbemD7A54USSEp4ifDPOzHYusiN42goVG1MOcTvWYzukZ9FKz+G9DGHOMpNFd0KaMGyAlsNf8Wsd0PBzaYBgCPZHhYtQgxrl5xl2tV6D38l2AaBXpnmfTbVyG+8Xx1/cnqACXC4aAlUqRrQPtKaJuuVraSimP/+AtlHHOmN1sSTvXtADZ8UT2RMeskSJEEoYBHbmEiNnviubDGnAB9zFykEuPBDCZjvAklWWGxMrSVvOVOMkKkXXIgqUr5PIAOBB7esw6Jdxer08oBSLIYcNTzWUqu+DREyrKnPVgaDIVCKcMYyMRe9d5FaluN1Jus2T/Je6JxdChV8oQBp6Rxx4owlLDAI9ZQumrEFjtqIfpqjoI2ZqEgveRsdJr0gcBime3gXU3Q29dC71kPvWul5224kA7qHzxOcVUf4DGzlFx7gD6snJLwgB36gASPD3Kn+MkwqN6WYODK5dy0JWzsH5+2F87ta5Uk3j2zbxfuhdDBk6GcU+oQ2WVImhs6cfckYOkJRCwdFm+xszB4h70zUF6YX2dw6JJHgqdqwyXKByTnyxHGrxmLs9JppFP1gzsR3/uInWnqdvFeoeSmGh9LEx95yRi1iQ5OhzHrWwXAE4yfp2jgyZybzDCPzJMitXEDu9scF/g9kORB97tIrD/f2+OlTmDOWQpW1TjoayfnXLz5XrG9RZnN9lXooi8uk+DJM3iqT4Mx98bhDR63SSrS7/7B44cNNUPkJseeAXPm1YMWjSL8aJ+mi4gdC12U6yXa1SxM20C8gMe2oSwa/pKnSOBw6zYAeOyuI/+8zI48U/TEOlsX/WnWnYdUw9ezvOMuAfK53siyQTagjgPZa1wuxwBnzLquInncJivH78HAo8VgNj+JUZsv9mZhpoHIfZokEOZerx6f4/KS3iXPWTaJdwHzf5DaE0Ln8RIM5mMRh+qWwOAhNlT9jUbfdHLCIqtNhjgujzs2sP/GN3gY1XbIfdoSVuvVZ6rZjrJKJlXAUwDaZAfpbbodo3bc4RtATlwwZQ2waRcHcgKGD54Sn7Yk+MQ2bLYX9bSUXla5C3C9xpPNLpjkEU/vj+eJ9a3zpvtkglK6CYQCO/0r8iRGDbyl2xQDPGRwY6s+5V/yeHFPkBti4uVg4z6EyNbHbI5HMc0uWSd+9y7JC5SsORVs3DFgra8ou1JCAI89aopEY5u/bXvIg7yodFBqM65FasJJ4KjytJX5p5UrTr0tz3YeWdlPOFY7Noh8t+ju56H3/MVOXQrD0EeSxmoB1+LitMlrTxROaq5ZouJQOk3IBZDhgUdWrItuuU3NEltoYNKS2zX+LEQbvuZJmfYieVTZUANLHi8KcyZ4HIqV3iZYfRsR20P8PY/bgkgfre4DdA4oVgtMikaIzgHxEmHsybBGL7BPmlQ4hvK2Nv9H6SWPc1QWlXqb7w7+hchtjIKhKN1GVMkpkB+VeVRXpZUbyElYhpInXWMrIwSjtwl611qxvVidbyBmvOm6YVE2rhU7EtbY42DWHi9Is7R4rUgXEsmZNK9DDR7xRVAI6JZH7BQOjA6u8Mk9mcI49Enni5csVCLai+RRJbQMLHk86jxUkNaJBxqEjAxCBlMzoSf3wuzeCZ5qR9zogMXtKoGWVttPmFU1RRhRCxNG2brrkEke50UdTzTx7AlK3aD7dEY6Cq//St60Y79GwmIWa/N8VM/ctpSq+ykYVQXziHPlO4CUCXgcCUSczJHdT9s1tPKwfbrKXKdBOs33SwWNege85FGeMK8Nywg88nwpwhmMXX+Atmu5ekBVofdOrUZy2p3CK2+zZ/R/SX4lj1t8cGALc5jblldMKLcvO/AIGWRTmvRsgLn9IYxufdC7KyNzApx4mYabEJnwmUFkABXJo4yWrIZlCR45RkmHy7YvR4xSkgMo08KlEVs4KKPSr+QZ3jqPX7Bk31fO4HEwRKex3X8KblA0m2FOvBypg64bsH0VQ/IE3rb82nmUFOYRBJ60zt/xBiKb71TPcsieI0lFkpp/Vzpvy5E8Xqsb2zpPdV5XSGDwVHSesBDerwehfRW0LT/wDSCqfpeqv2NAMRP/7ol8XnVisC+tb6ugnSfMZRjQV8m3LUkfJ5Pb0lJF/t1JenP+pN+52SWaOSclSj6r3vh1fy4NGYNszr893V9l2/KLrlKCR6arRrY9REnDnkYsKtjUnWKXEvBaLjHr5JXURsFc+Ot0HlgxwBNY8lR0nuyTXQy8aw1E9gQZAD1cXTNuhT7lwgF1FrBhiV0e2gM/DD2Sti7j4N+l866KAZ6KzpN7cf171QOk3hizvpNVsU5DOoHQYwCU0Hvm/jItySrg8fAVD5nOExA85sSzBzk4tfdvQaz1CbVCa86Lp1bDmP10GozFUJgrkqfMJE8u8Ji7n0Zi8zXewlmJ7GD6beDTvyzA6EXylH08zwFr5wld8tgkBYn3F3uSu+njug/wqIZkVCRPmUgeWrDczKG23hN59zpPlC39tp6LhJHPi+QZSJxUMRKWLp4nbMkjj/7aRnUGcvE9mM2gktHONugFPGWZq+45nseToC7QuMR2nvRR3cv4ibev/psDjur27RnETMlNyhGIJHkEO9iE09M6j6p7gsAjWOWJcSs+LU+Eoj2pVWs/bQ/TjTggaN7WiAFPahsSa8/zlOtDOVrEkjGYQ9kHP0+mnUcmDHriE5b8PKk5y6CNOS63b0uLQd/zDErGzzMiwAObv5eq/kbNZvcv0pFOBJ7xnwdvuGWwvGpf5VlhJr2lr/EPQGK23Z9HZjBn6zLnfX8wozxtzalt0Nd8QS1nS/hdDBABt9l4vyiyEij15oA9bVGyX1yHvvJ8OzFNNVcr2xueASG29ad2rI+qlVmQOzXCPPinkqgAgl1M6E1eEhCd7UtmaQgMmF0w21Z69/xzQ5T2tv1tundCy5EheexMCTQtQ2znMm+GPfF1NiI142KwWL2Aj7X7txjlhcxSKst9Ey61pZgTbMa64ZkYUwygA1TjK5lohM7GiYqE6Xpcqh+G7Cc5ZbHMfiDfXcwb0cFIAQ8tmO/6Dg4VrpQ8OimiOpFcql/pY79ggRcrJ/5LJ66oHx5mue2kR+CmHGcPNR8Pc8UxmmNRJQWuch63Oi7cWzpblmAYW5DRvp8B3tN26v5E9xZORZ5DH0wTS3rmYR4xkoemkwC09adFLUuUa9Ucsmw7jysxqImgp9vxg+A5Y+6Q6W+RWo3uBa8MqJNVAU/BCQyJJcPLIkmlm4p62GUae7Mqw2SMyYPNyMsQBrU1m9E57l8RmfujjLHYwXKeyJ1GlOQRs5jhl1Khmw20SnY9huTkG3LYivo7dopyxN7+hDdHq5+xyXpb5qHPDjI2eq56MxLBIwrAbn/KrhpsdanbfbwulvzCo/N+KO0y+Wuvh543n2usEjhkaIyM//igPLLKtqW0wLJU5PanEN95t32H19OK23OU3AmDOxGlIncvD57ynN211QEemYSehpugjf1kztFXwOO2qOnfbSJube+LIJ6eMKsbE9u5QcVFqPJdokF5RLSlklTU9qxAasc9SBhb1I2QhZ5C20viI+DTrwbqji9c3biSeqO6XnZ6MWVFaNuWI9q5wn9ddWkLIm6Z5NSLodWeUpDyNv8IbQDx/WtEFZugY4K5DqnaK5Cqv9IVyBXJo4qbjHZC30jtF1KIvvhY7zo1WjRZmAPWJhApUde0GxAjI6CQNgHqTMmxieovO56x+YOsPkGt5pr6LGnYiFWLgEy1wqyJZ6RtOYWmZ4DC7BSAK3SD1YKyyNtSGKv/AHglQNncMQw9wk/E970Mrf1F6GafXUwjs2aC1I8MrQ5s1CGCJ8+acCpYZBwsQa/mcMx4I7jMrYjEQBTAqf1vIrbnBfCu9Yig22YfzbootYexMdDGnYi+8SeBTTgBuqVn5J8VLnEgXDgbloB1SmJKl3mjQiN84sWyNod6KSql5SjYSJo3tvwE0c7XkAIFxxW+igyeTFFkU/0TkHj3ViDZimhqX5rJSmPVSFZNF6V/EJksAVPMybNtMGJMyT0w+1oE71801SP+LWn2IBKdBl5VD1Y1JQPE3seksTZwxPs1Q8uApRE9TP6rUPqz26L6/93WWRlnb02DAAAAiklEQVT6Bow3X3+lA0//1NnUcPkuIWFCkC6eZtCWkBQ+QZeQdJmXLE/gqcsBje1++y/qP/Pfsgqs2aPw/7hAd2aPNX9nQwCeQG9WubmMZqACnjJajOE2lAp4htuKldF4K+Apo8UYbkOpgGe4rVgZjbcCnjJajOE2lAp4htuKldF4K+Apo8UYbkP5f+ia3Hn5jPk0AAAAAElFTkSuQmCC" alt="个人照片" loading="eager" onerror="this.style.display='none';">
    </section>











         <section class="section" id="internship-section">
           <div class="section-header">
              <div class="section-title">
                <i class="fas fa-id-badge"></i>
                <!-- <h2>实习经历</h2> -->
                <h2>实习经历</h2>
              </div>

           </div>
           <div class="section-content">

               <div class="section-item">
                 <div class="item-header three-column">
                   <span>简历本信息技术有限公司</span>
                   <span>市场部经理</span>
                   <span class="date-range">2022-04 - 2024-04</span>
                 </div>
                 <div class="item-description">
                    <p>在本公司负责所有关于赛事活动策划工作、并完成独立完成各种活动执行工作及赛后活动总结等工作。
参与市级飞镖赛、定向赛、路跑等活动；并帮助政府完成一系列政府采购服务，包括运动会、五四青年节活动、元宵节活动等；</p>
                 </div>
               </div>

               <div class="section-item">
                 <div class="item-header three-column">
                    <span>简技术有限公司  - 市场部经理</span>
                    <span>市场部经理</span>
                    <span class="date-range">2022-04 - 2024-04</span>
                  </div>
                  <div class="item-description">
                     <p>在本公司负责所有关于赛事活动策划工作、并完成独立完成各种活动执行工作及赛后活动总结等工作。
 参与市级飞镖赛、定向赛、路跑等活动；并帮助政府完成一系列政府采购服务，包括运动会、五四青年节活动、元宵节活动等；</p>
                  </div>
               </div>

           </div>
         </section>












        <section class="section" id="education-section">
          <div class="section-header">
            <div class="section-title">
                <i class="fas fa-graduation-cap"></i>
                <h2>教育背景</h2>

            </div>

          </div>
          <div class="section-content">

              <div class="section-item">
                <div class="item-header three-column">
                  <span>学校1</span>
                  <span>专业1 (本科)</span>
                  <span class="date-range">2025-04 - 2025-04</span>
                </div>

                <div class="item-header three-column">
                  <span>学校2学校2学校2学校</span>
                  <span>专业1 (本科)</span>
                  <span class="date-range">2025-04 - 至今</span>
                </div>

              </div>

          </div>
        </section>






        <section class="section" id="school-section">
          <div class="section-header">
            <div class="section-title">
                <i class="fas fa-school"></i>
                <h2>在校经历</h2>

            </div>
          </div>
          <div class="section-content">
            <div class="long-text-item">
                <span class="date-prefix">2023-04:</span>
                <span>
                    在本公司负责所有关于赛事活动策划工作、并完成独立完成各种活动执行工作及赛后活动总结等工作。&lt;/p&gt;&lt;p&gt;参与市级飞镖赛、定向赛、路跑等活动；并帮助政府完成一系列政府采购服务，包括运动会、五四青年节活动、元宵节活动等&lt;/p&gt;
                </span>

            </div>
            <div class="long-text-item">
                <span class="date-prefix">2024-04:2025-05</span>
                <span>在本公司负责所有关于赛事活动策划工作、并完成独立完成各种活动执行工作及赛后活动总结等工作。&lt;/p&gt;&lt;p&gt;参与市级飞镖赛、定向赛、路跑等活动；并帮助政府完成一系列政府采购服务，包括运动会、五四青年节活动、元宵节活动等&lt;/p&gt;</span>

            </div>
          </div>
        </section>












        <section class="section" id="work-section">
          <div class="section-header">
            <div class="section-title">
                <i class="fas fa-briefcase"></i>
                <h2>工作经历</h2>

            </div>
          </div>
          <div class="section-content">

              <div class="section-item">
                <div class="item-header three-column">
                  <span>简历本信息技术有限公司1</span>
                  <span>产品/品牌经理</span>
                  <span class="date-range">2023-04 - 2025-04</span>
                </div>
                <div class="item-description">
                    <p>这是工作经历1 的具体内容, 这是工作经历1 的具体内容, 这是工作经历1 的具体内容, 这是工作经历1 的具体内容, 这是工作经历1 的具体内容, 这是工作经历1 的具体内容, 这是工作经历1 的具体内容, 这是工作经历1 的具体内容, </p>

                </div>
              </div>

              <div class="section-item">
                <div class="item-header three-column">
                  <span>术有限公司2</span>
                  <span>品牌经理</span>
                  <span class="date-range">2023-04 - 2025-04</span>
                </div>
                <div class="item-description">
                    <p>这是工作经历1 的具体内容, 这是工作经历1 的具体内容, 这是工作经历1 的具体内容, 这是工作经历1 的具体内容, 这是工作经历1 的具体内容, 这是工作经历1 的具体内容, 这是工作经历1 的具体内容, 这是工作经历1 的具体内容, </p>

                </div>
              </div>
          </div>
        </section>


        <section class="section" id="skills-section">
          <div class="section-header">
            <div class="section-title">
                <i class="fas fa-certificate"></i>
                <h2>技能证书</h2>

            </div>
          </div>
          <div class="section-content">
                <ul class="horizon-item-list">
                    <li>吃鸡</li>

                    <li>吃饭</li>

                    <li>喝酒</li>

                    <li>hahassfh</li>

                </ul>


          </div>
        </section>



        <section class="section " id="project-section">
          <div class="section-header">
            <div class="section-title">
                <i class="fas fa-project-diagram"></i>
                <h2>项目经历</h2>

            </div>
          </div>
          <div class="section-content">

              <div class="section-item">
                <div class="item-header three-column">
                  <span>成都市高新南区法院工程项目</span>
                  <span>项目经理</span>
                  <span class="date-range">2023-04 - 2024-04</span>
                </div>

                  <div class="item-description">
                    <p>分析和总结市场反馈信息，配合协调施出资方与公司的关系，供应商与公司的关系，定期提交项目阶段成果报告，对销售价格与成本进行管理控制。提供高效快速的销售服务及技术支持最终取得工程顺利完工。</p>
                  </div>
              </div>

              <div class="section-item">
                <div class="item-header three-column">
                  <span>索尼 2012 Handycam High-end Seminar for DSLR user (客户群体)</span>
                  <span>项目经理</span>
                  <span class="date-range">2024-04 - 至今</span>
                </div>

                  <div class="item-description">
                    <p>1.负责活动推广方案策划2.设计巡展产品技术展示环节、现场产品展示区，提供产品展示技术解决方案 3.担任教学样片摄制工作4.担任巡展技术讲师5.现场体验Q&amp;A技术指导、用户体验反馈收集分析</p>
</p>
                  </div>

              </div>

          </div>
        </section>




        <section class="section " id="interests-section">
          <div class="section-header">
            <div class="section-title">
                <i class="fas fa-heart"></i>
                <h2>兴趣爱好</h2>

            </div>
          </div>
          <div class="section-content">
            <ul class="horizon-item-list">

                <li>custom</li>

                <li>LinuxLinuxLinuxLinuxLinux</li>

                <li>Windows</li>

            </ul>
          </div>
        </section>



        <section class="section " id="self-evaluation-section">
          <div class="section-header">
            <div class="section-title">
                <i class="fas fa-comment"></i>
                <h2>自我评价</h2>

            </div>
          </div>
          <div class="section-content">


                 <div class="long-text-item">

                     <p>我本人很喜欢装修这个行业，我也养成有项目必每天上工地的习惯。我本人性格外向，积极活泼，善于沟通，有一定有脉。我也心性格，偶尔有点小脾气，希望在以后的工作努力改变。我不是一个运筹帷握决胜千里的帅才，我是一个一夫当关万夫莫开的将才。

                     </p>

</div>

          </div>
        </section>












        <section class="section " id="awards-section">
          <div class="section-header">
            <div class="section-title">
                <i class="fas fa-award"></i>
                <h2>奖项荣誉</h2>
            </div>
          </div>
          <div class="section-content">
            <ul class="horizon-item-list">

                <li>CFA</li>

                <li>基金</li>

                <li>证券</li>

                <li>JAVA</li>

            </ul>
          </div>
        </section>


          <section class="section" id="custom1-section">
            <div class="section-header">
                <div class="section-title">
                    <i class="fas fa-star"></i>
                    <h2>商业模式</h2>
                </div>
            </div>
            <div class="section-content">

                <div class="section-item">
                   <div class="item-header two-column">
                     <span>商业模式 </span>

                     <span class="date-range">2023-04-23 - 2025-04-23</span>
                   </div>

                     <div class="item-description">
                        <p>我不是一个运筹帷握决胜千里的帅才，我是一个一夫当关万夫莫开的将才。</p>
                     </div>

                </div>

            </div>
          </section>

          <section class="section" id="custom2-section">
            <div class="section-header">
                <div class="section-title">
                    <i class="fas fa-star"></i>
                    <h2>商业模式</h2>
                </div>
            </div>
            <div class="section-content">

                <div class="section-item">
                   <div class="item-header two-column">
                     <span>商业模式 </span>

                     <span class="date-range">2023-04-23 - 2025-04-23</span>
                   </div>

                     <div class="item-description">
                        <p>我不是一个运筹帷握决胜千里的帅才，我是一个一夫当关万夫莫开的将才。</p>
                     </div>

                </div>

            </div>
          </section>

          <section class="section" id="custom3-section">
            <div class="section-header">
                <div class="section-title">
                    <i class="fas fa-star"></i>
                    <h2>账户管理</h2>

                </div>
            </div>
            <div class="section-content">

                <div class="section-item">
                   <div class="item-header two-column">
                     <span>账户管理 (社交网络)</span>
                     <span class="date-range">2024-04-24 - 至今</span>
                    </div>

                     <div class="item-description">
                         <p>很喜欢装修这个行业，我也养成有项目必每天上工地的习惯。我本人性格外向，积极活泼，善于沟通</p>
                        </div>

                </div>

            </div>
          </section>


  </div>
</body>
</html>