<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>{{ resume.basicInfo.name | default("我的") }}的简历</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <style>
    :root {
      --theme-color: {{ theme_color | default('#2E75B6') }};
      --base-font-size: {{ base_font_size | default(11) }}pt;
      --max-font-size: {{ max_font_size | default(12) }}pt;
      --spacing: {{ spacing | default(1.2) }};
      --text-color: #333333;
      --secondary-text-color: #555555;
      --border-color: #e0e0e0;
    }

    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", Arial, sans-serif;
    }

    body {
      background-color: #f0f0f0;
      display: flex;
      justify-content: center;
      padding: 10px 0;
      overflow-x: hidden;
    }

    .resume-container {
      width: 210mm;
      min-height: 297mm;
      background-color: white;
      box-shadow: 0 0 15px rgba(0, 0, 0, 0.15);
      display: flex;
      flex-direction: column;
      overflow: hidden;
      font-size: var(--base-font-size);
      line-height: var(--spacing);
      color: var(--text-color);
      position: relative;
    }

    /* 左侧装饰条 */
    .side-decoration {
      position: absolute;
      left: 0;
      top: 0;
      width: 15px;
      height: 100%;
      background-color: var(--theme-color);
    }

    /* 头部区域 */
    .resume-header {
      padding: 30px 40px 20px 40px;
      border-bottom: 2px solid var(--theme-color);
      margin-left: 15px;
    }

    .resume-header-name {
      font-size: calc(var(--base-font-size) * 2.2);
      font-weight: bold;
      margin-bottom: 15px;
      color: var(--theme-color);
    }

    .resume-header-info {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
      gap: 10px;
      font-size: var(--base-font-size);
    }

    .resume-header-info p {
      display: flex;
      align-items: center;
    }

    .resume-header-info i {
      margin-right: 8px;
      color: var(--theme-color);
      width: 16px;
      text-align: center;
    }

    /* 主体内容区域 */
    .resume-content {
      padding: 0 40px 30px 40px;
      margin-left: 15px;
    }

    /* 各部分通用样式 */
    .section {
      margin-top: 20px;
    }

    .section-header {
      display: flex;
      align-items: center;
      margin-bottom: 10px;
      position: relative;
    }

    .section-title {
      font-size: calc(var(--base-font-size) * 1.3);
      font-weight: bold;
      color: var(--theme-color);
      padding-right: 15px;
      display: flex;
      align-items: center;
    }

    .section-title i {
      margin-right: 8px;
    }

    .section-line {
      flex-grow: 1;
      height: 1px;
      background-color: var(--theme-color);
    }

    /* 教育经历样式 */
    .education-item {
      margin-bottom: 15px;
    }

    .education-header {
      display: flex;
      justify-content: space-between;
      font-weight: bold;
      margin-bottom: 5px;
    }

    .education-school {
      color: var(--theme-color);
    }

    .education-date {
      color: var(--secondary-text-color);
    }

    .education-details {
      margin-left: 20px;
    }

    /* 工作经历样式 */
    .experience-item {
      margin-bottom: 15px;
    }

    .experience-header {
      display: flex;
      justify-content: space-between;
      font-weight: bold;
      margin-bottom: 5px;
    }

    .experience-company {
      color: var(--theme-color);
    }

    .experience-date {
      color: var(--secondary-text-color);
    }

    .experience-position {
      font-weight: bold;
      margin-bottom: 5px;
      margin-left: 20px;
    }

    .experience-description {
      margin-left: 20px;
    }

    /* 项目经历样式 */
    .project-item {
      margin-bottom: 15px;
    }

    .project-header {
      display: flex;
      justify-content: space-between;
      font-weight: bold;
      margin-bottom: 5px;
    }

    .project-name {
      color: var(--theme-color);
    }

    .project-date {
      color: var(--secondary-text-color);
    }

    .project-role {
      font-weight: bold;
      margin-bottom: 5px;
      margin-left: 20px;
    }

    .project-description {
      margin-left: 20px;
    }

    /* 实习经历样式 */
    .internship-item {
      margin-bottom: 15px;
    }

    .internship-header {
      display: flex;
      justify-content: space-between;
      font-weight: bold;
      margin-bottom: 5px;
    }

    .internship-company {
      color: var(--theme-color);
    }

    .internship-date {
      color: var(--secondary-text-color);
    }

    .internship-position {
      font-weight: bold;
      margin-bottom: 5px;
      margin-left: 20px;
    }

    .internship-description {
      margin-left: 20px;
    }

    /* 在校经历样式 */
    .school-experience-item {
      margin-bottom: 10px;
      padding-left: 20px;
    }

    .school-experience-date {
      font-weight: bold;
      color: var(--theme-color);
    }

    /* 技能列表样式 */
    .skills-list {
      display: flex;
      flex-wrap: wrap;
      gap: 15px;
      padding-left: 20px;
    }

    .skill-item {
      background-color: #f5f5f5;
      padding: 5px 12px;
      border-radius: 4px;
      border-left: 3px solid var(--theme-color);
    }

    /* 奖项荣誉样式 */
    .awards-list {
      padding-left: 20px;
    }

    .award-item {
      margin-bottom: 8px;
      position: relative;
      padding-left: 15px;
    }

    .award-item:before {
      content: "•";
      position: absolute;
      left: 0;
      color: var(--theme-color);
      font-weight: bold;
    }

    /* 兴趣爱好样式 */
    .interests-list {
      display: flex;
      flex-wrap: wrap;
      gap: 15px;
      padding-left: 20px;
    }

    .interest-item {
      background-color: #f5f5f5;
      padding: 5px 12px;
      border-radius: 4px;
      border-left: 3px solid var(--theme-color);
    }

    /* 自我评价样式 */
    .evaluation-content {
      padding-left: 20px;
    }

    /* 自定义模块样式 */
    .custom-item {
      margin-bottom: 15px;
    }

    .custom-header {
      display: flex;
      justify-content: space-between;
      font-weight: bold;
      margin-bottom: 5px;
    }

    .custom-name {
      color: var(--theme-color);
    }

    .custom-date {
      color: var(--secondary-text-color);
    }

    .custom-role {
      font-weight: bold;
      margin-bottom: 5px;
      margin-left: 20px;
    }

    .custom-content {
      margin-left: 20px;
    }

    /* 隐藏空模块 */
    .hidden {
      display: none;
    }

    /* 打印样式 */
    @page {
      size: A4;
      margin: 0;
    }

    @media print {
      body {
        background-color: white;
        padding: 0;
      }

      .resume-container {
        box-shadow: none;
        width: 100%;
        min-height: 0;
      }

      .section, .section-header {
        break-inside: avoid;
        page-break-inside: avoid;
      }

      .experience-item, .project-item, .education-item,
      .internship-item, .school-experience-item, .custom-item {
        break-inside: avoid;
        page-break-inside: avoid;
      }
    }
  </style>
</head>
<body>
  <div class="resume-container">
    <!-- 左侧装饰条 -->
    <div class="side-decoration"></div>

    <!-- 头部区域 -->
    <header class="resume-header">
      <h1 class="resume-header-name">{{ resume.basicInfo.name | default('') }}</h1>
      <div class="resume-header-info">
        {% if resume.basicInfo.gender %}<p><i class="fas fa-user"></i>{{ resume.basicInfo.gender }}</p>{% endif %}
        {% if resume.basicInfo.age %}<p><i class="fas fa-birthday-cake"></i>{{ resume.basicInfo.age }}岁</p>{% endif %}
        {% if resume.basicInfo.educationLevel %}<p><i class="fas fa-graduation-cap"></i>{{ resume.basicInfo.educationLevel }}</p>{% endif %}
        {% if resume.basicInfo.phone %}<p><i class="fas fa-phone"></i>{{ resume.basicInfo.phone }}</p>{% endif %}
        {% if resume.basicInfo.email %}<p><i class="fas fa-envelope"></i>{{ resume.basicInfo.email }}</p>{% endif %}
        {% if resume.basicInfo.city %}<p><i class="fas fa-map-marker-alt"></i>{{ resume.basicInfo.city }}</p>{% endif %}
        {% if resume.basicInfo.wechat %}<p><i class="fab fa-weixin"></i>{{ resume.basicInfo.wechat }}</p>{% endif %}
        {% if resume.jobIntention and resume.jobIntention.position %}<p><i class="fas fa-bullseye"></i>求职意向: {{ resume.jobIntention.position }}</p>{% endif %}
      </div>
    </header>

    <!-- 主体内容区域 -->
    <main class="resume-content">
      <!-- 求职意向 -->
      {% if resume.jobIntention and (resume.jobIntention.city or resume.jobIntention.salary or resume.jobIntention.status) %}
      <section class="section" id="job-intention-section">
        <div class="section-header">
          <h2 class="section-title"><i class="fas fa-bullseye"></i>求职意向</h2>
          <div class="section-line"></div>
        </div>
        <div class="section-content" style="padding-left: 20px;">
          {% if resume.jobIntention.city %}<p><strong>期望城市：</strong>{{ resume.jobIntention.city }}</p>{% endif %}
          {% if resume.jobIntention.salary %}<p><strong>期望薪资：</strong>{{ resume.jobIntention.salary }}</p>{% endif %}
          {% if resume.jobIntention.status %}<p><strong>求职状态：</strong>{{ resume.jobIntention.status }}</p>{% endif %}
        </div>
      </section>
      {% endif %}

      <!-- 根据moduleOrders排序显示各个模块 -->
      {% for module in ordered_modules %}
        {% if module.key == 'education' and module.data %}
        <section class="section" id="education-section">
          <div class="section-header">
            <h2 class="section-title"><i class="{{ module.icon | default('fas fa-graduation-cap') }}"></i>{{ module.title | default('教育背景') }}</h2>
            <div class="section-line"></div>
          </div>
          <div class="section-content">
            {% for item in module.data %}
            <div class="education-item">
              <div class="education-header">
                <span class="education-school">{{ item.school }}</span>
                <span class="education-date">{{ item.startDate }} - {{ item.endDate }}</span>
              </div>
              <div class="education-details">
                <p>{{ item.major }} ({{ item.degree }})</p>
                {% if item.description %}<p>{{ item.description }}</p>{% endif %}
              </div>
            </div>
            {% endfor %}
          </div>
        </section>
        {% endif %}

        {% if module.key == 'work' and module.data %}
        <section class="section" id="work-section">
          <div class="section-header">
            <h2 class="section-title"><i class="{{ module.icon | default('fas fa-briefcase') }}"></i>{{ module.title | default('工作经历') }}</h2>
            <div class="section-line"></div>
          </div>
          <div class="section-content">
            {% for item in module.data %}
            <div class="experience-item">
              <div class="experience-header">
                <span class="experience-company">{{ item.company }}</span>
                <span class="experience-date">{{ item.startDate }} - {{ item.endDate }}</span>
              </div>
              <div class="experience-position">{{ item.position }}</div>
              {% if item.description %}
              <div class="experience-description">{{ item.description | safe }}</div>
              {% endif %}
            </div>
            {% endfor %}
          </div>
        </section>
        {% endif %}

        {% if module.key == 'internship' and module.data %}
        <section class="section" id="internship-section">
          <div class="section-header">
            <h2 class="section-title"><i class="{{ module.icon | default('fas fa-id-badge') }}"></i>{{ module.title | default('实习经历') }}</h2>
            <div class="section-line"></div>
          </div>
          <div class="section-content">
            {% for item in module.data %}
            <div class="internship-item">
              <div class="internship-header">
                <span class="internship-company">{{ item.company }}</span>
                <span class="internship-date">{{ item.startDate }} - {{ item.endDate }}</span>
              </div>
              <div class="internship-position">{{ item.position }}</div>
              {% if item.content %}
              <div class="internship-description">{{ item.content | safe }}</div>
              {% endif %}
            </div>
            {% endfor %}
          </div>
        </section>
        {% endif %}

        {% if module.key == 'project' and module.data %}
        <section class="section" id="project-section">
          <div class="section-header">
            <h2 class="section-title"><i class="{{ module.icon | default('fas fa-project-diagram') }}"></i>{{ module.title | default('项目经历') }}</h2>
            <div class="section-line"></div>
          </div>
          <div class="section-content">
            {% for item in module.data %}
            <div class="project-item">
              <div class="project-header">
                <span class="project-name">{{ item.projectName }}</span>
                <span class="project-date">{{ item.startDate }} - {{ item.endDate }}</span>
              </div>
              <div class="project-role">{{ item.role }}</div>
              {% if item.description %}
              <div class="project-description">{{ item.description | safe }}</div>
              {% endif %}
            </div>
            {% endfor %}
          </div>
        </section>
        {% endif %}

        {% if module.key == 'school' and module.data %}
        <section class="section" id="school-section">
          <div class="section-header">
            <h2 class="section-title"><i class="{{ module.icon | default('fas fa-school') }}"></i>{{ module.title | default('在校经历') }}</h2>
            <div class="section-line"></div>
          </div>
          <div class="section-content">
            {% for item in module.data %}
            <div class="school-experience-item">
              <span class="school-experience-date">{{ item.startDate }}{% if item.endDate and item.endDate != item.startDate %} - {{ item.endDate }}{% endif %}{% if item.role %} ({{ item.role }}){% endif %}:</span>
              <span>{{ item.content | safe }}</span>
            </div>
            {% endfor %}
          </div>
        </section>
        {% endif %}

        {% if module.key == 'skills' and module.data %}
        <section class="section" id="skills-section">
          <div class="section-header">
            <h2 class="section-title"><i class="{{ module.icon | default('fas fa-certificate') }}"></i>{{ module.title | default('技能证书') }}</h2>
            <div class="section-line"></div>
          </div>
          <div class="section-content">
            <div class="skills-list">
              {% for skill in module.data %}
              <div class="skill-item">{{ skill }}</div>
              {% endfor %}
            </div>
          </div>
        </section>
        {% endif %}

        {% if module.key == 'awards' and module.data %}
        <section class="section" id="awards-section">
          <div class="section-header">
            <h2 class="section-title"><i class="{{ module.icon | default('fas fa-award') }}"></i>{{ module.title | default('奖项荣誉') }}</h2>
            <div class="section-line"></div>
          </div>
          <div class="section-content">
            <div class="awards-list">
              {% for award in module.data %}
              <div class="award-item">{{ award }}</div>
              {% endfor %}
            </div>
          </div>
        </section>
        {% endif %}

        {% if module.key == 'interests' and module.data %}
        <section class="section" id="interests-section">
          <div class="section-header">
            <h2 class="section-title"><i class="{{ module.icon | default('fas fa-heart') }}"></i>{{ module.title | default('兴趣爱好') }}</h2>
            <div class="section-line"></div>
          </div>
          <div class="section-content">
            <div class="interests-list">
              {% for interest in module.data %}
              <div class="interest-item">{{ interest }}</div>
              {% endfor %}
            </div>
          </div>
        </section>
        {% endif %}

        {% if module.key == 'evaluation' and module.data %}
        <section class="section" id="evaluation-section">
          <div class="section-header">
            <h2 class="section-title"><i class="{{ module.icon | default('fas fa-comment') }}"></i>{{ module.title | default('自我评价') }}</h2>
            <div class="section-line"></div>
          </div>
          <div class="section-content">
            <div class="evaluation-content">
              {% for eval_item in module.data %}
              <p>{{ eval_item.content | safe if eval_item.content else eval_item | safe }}</p>
              {% endfor %}
            </div>
          </div>
        </section>
        {% endif %}

        {% if module.key == 'custom1' and module.data %}
        <section class="section" id="custom1-section">
          <div class="section-header">
            <h2 class="section-title"><i class="{{ module.icon | default('fas fa-star') }}"></i>{{ module.title | default('自定义模块1') }}</h2>
            <div class="section-line"></div>
          </div>
          <div class="section-content">
            {% for item in module.data %}
            <div class="custom-item">
              <div class="custom-header">
                <span class="custom-name">{{ item.customName }}</span>
                <span class="custom-date">{{ item.startDate }}{% if item.endDate %} - {{ item.endDate }}{% endif %}</span>
              </div>
              {% if item.role %}
              <div class="custom-role">{{ item.role }}</div>
              {% endif %}
              {% if item.content %}
              <div class="custom-content">{{ item.content | safe }}</div>
              {% endif %}
            </div>
            {% endfor %}
          </div>
        </section>
        {% endif %}

        {% if module.key == 'custom2' and module.data %}
        <section class="section" id="custom2-section">
          <div class="section-header">
            <h2 class="section-title"><i class="{{ module.icon | default('fas fa-star') }}"></i>{{ module.title | default('自定义模块2') }}</h2>
            <div class="section-line"></div>
          </div>
          <div class="section-content">
            {% for item in module.data %}
            <div class="custom-item">
              <div class="custom-header">
                <span class="custom-name">{{ item.customName }}</span>
                <span class="custom-date">{{ item.startDate }}{% if item.endDate %} - {{ item.endDate }}{% endif %}</span>
              </div>
              {% if item.role %}
              <div class="custom-role">{{ item.role }}</div>
              {% endif %}
              {% if item.content %}
              <div class="custom-content">{{ item.content | safe }}</div>
              {% endif %}
            </div>
            {% endfor %}
          </div>
        </section>
        {% endif %}

        {% if module.key == 'custom3' and module.data %}
        <section class="section" id="custom3-section">
          <div class="section-header">
            <h2 class="section-title"><i class="{{ module.icon | default('fas fa-star') }}"></i>{{ module.title | default('自定义模块3') }}</h2>
            <div class="section-line"></div>
          </div>
          <div class="section-content">
            {% for item in module.data %}
            <div class="custom-item">
              <div class="custom-header">
                <span class="custom-name">{{ item.customName }}</span>
                <span class="custom-date">{{ item.startDate }}{% if item.endDate %} - {{ item.endDate }}{% endif %}</span>
              </div>
              {% if item.role %}
              <div class="custom-role">{{ item.role }}</div>
              {% endif %}
              {% if item.content %}
              <div class="custom-content">{{ item.content | safe }}</div>
              {% endif %}
            </div>
            {% endfor %}
          </div>
        </section>
        {% endif %}
      {% endfor %}
    </main>
  </div>
</body>
</html>